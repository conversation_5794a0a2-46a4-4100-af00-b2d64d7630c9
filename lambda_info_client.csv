name,created_date,last_invocation,runtime,description,memory_size,timeout
client-application-mx-rc-authentication-migrate-user,2025-07-28 20:26:39 UTC,2025-07-28 19:00:00 UTC,nodejs20.x,,256,300
mytrends-cl-uat-validate-trends-in-alert,2024-11-04 11:16:44 UTC,2025-07-17 21:00:00 UTC,nodejs20.x,,128,30
client-application-mx-rc-documents-generate-pdf,2025-07-28 20:26:56 UTC,No disponible,nodejs18.x,,256,300
mytrends-mx-uat-send-email-on-trend-in-alert,2024-10-29 20:41:09 UTC,No disponible,nodejs20.x,,128,60
mytrends-cl-dev-validate-trends-in-alert,2024-10-29 20:06:15 UTC,No disponible,nodejs20.x,,128,30
client-application-cl-uat-authentication-verify-auth-challenge,2025-07-28 20:09:41 UTC,No disponible,nodejs20.x,,256,30
client-application-mx-dev-massive-payments-processor-main,2025-05-16 16:47:04 UTC,No disponible,nodejs20.x,,256,60
client-application-mx-rc-authentication-sync-user-event,2025-07-28 20:26:50 UTC,2025-07-28 19:00:00 UTC,nodejs20.x,,256,30
client-application-mx-dev-documents-generate-pdf,2025-07-28 19:49:59 UTC,2025-07-29 21:00:00 UTC,nodejs18.x,,256,300
client-application-cl-rc-documents-generate-pdf,2025-07-28 20:24:49 UTC,2025-07-15 19:00:00 UTC,nodejs18.x,,256,300
mytrends-mx-dev-get-all-trends,2024-10-29 20:05:50 UTC,No disponible,nodejs20.x,,128,60
mytrends-cl-uat-validate-trends-changed,2024-11-04 11:16:57 UTC,No disponible,nodejs20.x,,128,30
client-application-cl-dev-core-events-pipe-enrichment,2025-07-28 19:47:12 UTC,2025-07-29 19:00:00 UTC,nodejs20.x,,128,30
client-application-cl-rc-authentication-verify-auth-challenge,2025-07-28 20:23:48 UTC,No disponible,nodejs20.x,,256,30
client-application-mx-uat-authentication-sync-user-event,2025-07-28 20:11:27 UTC,2025-07-29 17:00:00 UTC,nodejs20.x,,256,30
client-application-cl-rc-authentication-create-auth-challenge,2025-07-28 20:23:50 UTC,No disponible,nodejs20.x,,256,30
client-application-mx-dev-authentication-migrate-users,2025-07-28 19:49:25 UTC,2025-06-16 13:00:00 UTC,nodejs20.x,,256,900
client-application-cl-rc-authentication-migrate-user,2025-07-28 20:23:52 UTC,2025-07-25 23:00:00 UTC,nodejs20.x,,256,300
client-application-mx-rc-apm-serverless-create-payment,2025-07-28 20:59:52 UTC,No disponible,nodejs20.x,,256,300
mytrends-cl-uat-send-email-on-trend-in-alert,2024-10-29 20:41:17 UTC,No disponible,nodejs20.x,,128,60
client-application-mx-rc-authentication-upsert-user,2025-07-28 20:26:43 UTC,2025-07-14 19:00:00 UTC,nodejs20.x,,256,300
client-application-mx-uat-authentication-migrate-users,2025-07-28 20:11:02 UTC,2025-06-24 21:00:00 UTC,nodejs20.x,,256,900
client-application-cl-dev-documents-generate-pdf,2025-07-28 19:47:15 UTC,2025-07-28 15:00:00 UTC,nodejs18.x,,256,300
client-application-cl-dev-authentication-check-sync-user,2025-07-28 19:47:06 UTC,2025-05-13 19:00:00 UTC,nodejs20.x,,256,900
client-application-cl-rc-authentication-create-user,2025-07-28 20:23:44 UTC,No disponible,nodejs20.x,,256,300
mytrends-mx-dev-remove-trends-without-alert,2024-10-29 20:06:20 UTC,No disponible,nodejs20.x,,128,30
client-application-cl-dev-authentication-upsert-user,2025-07-28 19:47:02 UTC,2025-06-24 15:00:00 UTC,nodejs20.x,,256,300
client-application-mx-rc-authentication-check-sync-user,2025-07-28 20:26:47 UTC,No disponible,nodejs20.x,,256,900
client-application-mx-rc-notifications,2025-07-29 16:58:29 UTC,2025-07-29 19:00:00 UTC,nodejs20.x,,256,60
client-application-mx-rc-authentication-sync-user,2025-07-28 20:26:41 UTC,2025-07-28 19:00:00 UTC,nodejs20.x,,256,180
client-application-mx-uat-notifications,2025-07-29 16:52:39 UTC,2025-07-29 17:00:00 UTC,nodejs20.x,,256,60
client-application-mx-rc-authentication-create-user,2025-07-28 20:26:34 UTC,No disponible,nodejs20.x,,256,300
client-application-mx-dev-authentication-assign-role,2025-07-28 19:49:39 UTC,2025-07-28 15:00:00 UTC,nodejs20.x,,256,30
client-application-mx-dev-authentication-define-auth-challenge,2025-07-28 19:49:41 UTC,No disponible,nodejs20.x,,256,30
client-application-mx-rc-authentication-migrate-users,2025-07-28 20:26:28 UTC,No disponible,nodejs20.x,,256,900
client-application-mx-dev-core-events-pipe-enrichment,2025-07-28 19:49:55 UTC,2025-07-29 17:00:00 UTC,nodejs20.x,,128,30
client-application-cl-uat-authentication-check-sync-user,2025-07-28 20:09:51 UTC,No disponible,nodejs20.x,,256,900
client-application-cl-uat-authentication-migrate-user,2025-07-28 20:09:44 UTC,2025-07-23 01:00:00 UTC,nodejs20.x,,256,300
client-application-cl-rc-authentication-sync-user-event,2025-07-28 20:24:07 UTC,2025-07-28 15:00:00 UTC,nodejs20.x,,256,30
client-application-cl-rc-authentication-remove-role,2025-07-28 20:23:39 UTC,No disponible,nodejs20.x,,256,30
client-application-mx-dev-authentication-create-user,2025-07-28 19:49:30 UTC,No disponible,nodejs20.x,,256,300
client-application-mx-dev-notifications,2025-07-28 19:50:02 UTC,2025-07-29 17:00:00 UTC,nodejs20.x,,256,60
client-application-cl-dev-authentication-remove-role,2025-07-28 19:46:53 UTC,2025-05-28 13:00:00 UTC,nodejs20.x,,256,30
client-application-cl-uat-authentication-sync-user-event,2025-07-28 20:09:54 UTC,2025-07-23 01:00:00 UTC,nodejs20.x,,256,30
client-application-mx-dev-authentication-remove-role,2025-07-28 19:49:28 UTC,2025-07-28 15:00:00 UTC,nodejs20.x,,256,30
mytrends-mx-dev-set-new-suppliers,2024-10-29 20:06:08 UTC,No disponible,nodejs20.x,,128,30
client-application-cl-dev-notifications,2025-07-28 19:47:19 UTC,2025-07-29 19:00:00 UTC,nodejs20.x,,256,60
client-application-mx-dev-wallet-processors-transfers,2025-07-28 19:50:04 UTC,2025-07-29 21:00:00 UTC,nodejs20.x,,256,300
client-application-cl-rc-authentication-assign-role,2025-07-28 20:24:00 UTC,No disponible,nodejs20.x,,256,30
client-application-cl-uat-authentication-create-auth-challenge,2025-07-28 20:09:43 UTC,No disponible,nodejs20.x,,256,30
mytrends-cl-uat-save-trends-in-alert,2024-11-04 11:16:50 UTC,No disponible,nodejs20.x,,128,30
client-application-mx-uat-authentication-define-auth-challenge,2025-07-28 20:11:25 UTC,No disponible,nodejs20.x,,256,30
client-application-cl-dev-authentication-pre-token-generation,2025-07-28 19:46:51 UTC,2025-07-29 19:00:00 UTC,nodejs20.x,,256,300
client-application-cl-dev-massive-payments-processor-main,2025-05-16 16:47:53 UTC,No disponible,nodejs20.x,,256,60
mytrends-mx-uat-remove-trends-without-alert,2024-11-04 11:16:48 UTC,No disponible,nodejs20.x,,128,30
aws-controltower-NotificationForwarder,2023-09-29 16:26:31 UTC,2025-07-25 13:00:00 UTC,python3.9,SNS message forwarding function for aggregating account notifications.,128,60
client-application-cl-uat-core-events-pipe-enrichment,2025-07-28 20:09:58 UTC,2025-07-29 19:00:00 UTC,nodejs20.x,,128,30
client-application-mx-uat-documents-generate-pdf,2025-07-28 20:12:37 UTC,2025-07-23 11:00:00 UTC,nodejs18.x,,256,300
client-application-mx-rc-authentication-verify-auth-challenge,2025-07-28 20:26:35 UTC,No disponible,nodejs20.x,,256,30
mytrends-mx-uat-validate-trends-in-alert,2024-11-04 11:16:54 UTC,No disponible,nodejs20.x,,128,30
client-application-cl-uat-documents-generate-pdf-react,2025-07-28 20:09:59 UTC,No disponible,nodejs18.x,,2048,300
client-application-mx-uat-authentication-pre-token-generation,2025-07-28 20:11:05 UTC,2025-07-29 19:00:00 UTC,nodejs20.x,,256,300
client-application-cl-dev-wallet-processors-payments-processor,2025-05-16 22:04:03 UTC,No disponible,nodejs20.x,,256,300
client-application-cl-uat-authentication-define-auth-challenge,2025-07-28 20:09:53 UTC,No disponible,nodejs20.x,,256,30
client-application-cl-dev-apm-serverless-create-payment,2025-07-29 17:50:34 UTC,2025-07-22 17:00:00 UTC,nodejs20.x,,256,300
mytrends-cl-dev-remove-trends-without-alert,2024-10-29 20:06:21 UTC,No disponible,nodejs20.x,,128,30
client-application-cl-dev-authentication-migrate-user,2025-07-28 19:46:59 UTC,2025-07-29 17:00:00 UTC,nodejs20.x,,256,300
mytrends-mx-uat-set-new-suppliers-blocklisted,2024-11-04 11:17:06 UTC,No disponible,nodejs20.x,,128,30
client-application-mx-rc-core-events-pipe-enrichment,2025-07-28 20:26:52 UTC,2025-07-29 19:00:00 UTC,nodejs20.x,,128,30
client-application-mx-dev-authentication-verify-auth-challenge,2025-07-28 19:49:31 UTC,No disponible,nodejs20.x,,256,30
client-application-cl-dev-authentication-migrate-users,2025-07-28 19:46:49 UTC,2025-06-16 13:00:00 UTC,nodejs20.x,,256,900
mytrends-mx-dev-save-trends-in-alert,2024-10-29 20:06:28 UTC,No disponible,nodejs20.x,,128,30
client-application-mx-uat-documents-generate-pdf-react,2025-07-28 20:12:36 UTC,No disponible,nodejs18.x,,2048,300
client-application-mx-dev-authentication-upsert-user,2025-07-28 19:49:37 UTC,2025-06-26 21:00:00 UTC,nodejs20.x,,256,300
mytrends-cl-dev-send-email-on-trend-in-alert,2024-10-29 20:06:40 UTC,No disponible,nodejs20.x,,128,60
client-application-mx-uat-authentication-verify-auth-challenge,2025-07-28 20:11:11 UTC,No disponible,nodejs20.x,,256,30
client-application-mx-rc-wallet-processors-transfers,2025-07-28 20:26:25 UTC,No disponible,nodejs20.x,,256,300
mytrends-cl-dev-validate-trends-changed,2024-10-29 20:06:28 UTC,No disponible,nodejs20.x,,128,30
client-application-mx-uat-authentication-upsert-user,2025-07-28 20:11:19 UTC,2025-06-24 13:00:00 UTC,nodejs20.x,,256,300
client-application-cl-uat-authentication-remove-role,2025-07-28 20:09:38 UTC,No disponible,nodejs20.x,,256,30
mytrends-cl-uat-set-new-suppliers-blocklisted,2024-11-04 11:17:03 UTC,No disponible,nodejs20.x,,128,30
mytrends-mx-uat-process-page,2024-11-04 11:16:35 UTC,No disponible,nodejs20.x,,128,60
mytrends-mx-uat-save-trends-in-alert,2024-11-04 11:17:00 UTC,No disponible,nodejs20.x,,128,30
mytrends-cl-dev-process-page,2024-10-29 20:05:57 UTC,No disponible,nodejs20.x,,128,60
client-application-cl-uat-authentication-assign-role,2025-07-28 20:09:49 UTC,2025-07-14 21:00:00 UTC,nodejs20.x,,256,30
client-application-cl-uat-documents-generate-pdf,2025-07-28 20:10:01 UTC,2025-07-23 11:00:00 UTC,nodejs18.x,,256,300
mytrends-mx-dev-validate-trends-in-alert,2024-10-29 20:06:02 UTC,No disponible,nodejs20.x,,128,30
client-application-mx-rc-authentication-pre-token-generation,2025-07-28 20:26:30 UTC,2025-07-28 23:00:00 UTC,nodejs20.x,,256,300
client-application-mx-uat-authentication-create-user,2025-07-28 20:11:09 UTC,No disponible,nodejs20.x,,256,300
client-application-mx-dev-authentication-check-sync-user,2025-07-28 19:49:40 UTC,2025-06-11 17:00:00 UTC,nodejs20.x,,256,900
client-application-mx-dev-apm-serverless-create-payment,2025-07-29 17:50:55 UTC,2025-07-23 19:00:00 UTC,nodejs20.x,,256,300
client-application-cl-rc-authentication-pre-token-generation,2025-07-28 20:23:33 UTC,2025-07-26 01:00:00 UTC,nodejs20.x,,256,300
client-application-mx-rc-authentication-create-auth-challenge,2025-07-28 20:26:37 UTC,No disponible,nodejs20.x,,256,30
client-application-mx-dev-authentication-create-auth-challenge,2025-07-28 19:49:33 UTC,No disponible,nodejs20.x,,256,30
client-application-cl-rc-authentication-define-auth-challenge,2025-07-28 20:24:04 UTC,No disponible,nodejs20.x,,256,30
mytrends-mx-uat-validate-trends-changed,2024-11-04 11:17:12 UTC,No disponible,nodejs20.x,,128,30
mytrends-mx-dev-process-page,2024-10-29 20:05:44 UTC,No disponible,nodejs20.x,,128,60
client-application-cl-rc-authentication-upsert-user,2025-07-28 20:23:57 UTC,2025-06-12 23:00:00 UTC,nodejs20.x,,256,300
client-application-mx-rc-authentication-define-auth-challenge,2025-07-28 20:26:48 UTC,No disponible,nodejs20.x,,256,30
client-application-mx-dev-authentication-migrate-user,2025-07-28 19:49:34 UTC,2025-07-29 17:00:00 UTC,nodejs20.x,,256,300
mytrends-mx-uat-get-all-trends,2024-11-04 11:16:29 UTC,No disponible,nodejs20.x,,128,60
client-application-cl-uat-authentication-pre-token-generation,2025-07-28 20:09:37 UTC,2025-07-29 17:00:00 UTC,nodejs20.x,,256,300
client-application-cl-dev-authentication-create-user,2025-07-28 19:46:54 UTC,No disponible,nodejs20.x,,256,300
client-application-mx-rc-documents-generate-pdf-react,2025-07-28 20:26:54 UTC,No disponible,nodejs18.x,,2048,300
client-application-cl-rc-notifications,2025-07-29 16:58:06 UTC,2025-07-29 15:00:00 UTC,nodejs20.x,,256,60
client-application-mx-uat-authentication-remove-role,2025-07-28 20:11:07 UTC,2025-07-08 21:00:00 UTC,nodejs20.x,,256,30
client-application-mx-uat-core-events-pipe-enrichment,2025-07-28 20:12:34 UTC,2025-07-29 17:00:00 UTC,nodejs20.x,,128,30
client-application-cl-rc-documents-generate-pdf-react,2025-07-28 20:24:47 UTC,No disponible,nodejs18.x,,2048,300
client-application-cl-dev-authentication-create-auth-challenge,2025-07-28 19:46:57 UTC,No disponible,nodejs20.x,,256,30
client-application-cl-dev-authentication-assign-role,2025-07-28 19:47:04 UTC,2025-07-28 15:00:00 UTC,nodejs20.x,,256,30
client-application-mx-dev-authentication-pre-token-generation,2025-07-28 19:49:26 UTC,2025-07-29 21:00:00 UTC,nodejs20.x,,256,300
client-application-mx-uat-authentication-assign-role,2025-07-28 20:11:21 UTC,2025-07-29 13:00:00 UTC,nodejs20.x,,256,30
client-application-cl-uat-authentication-sync-user,2025-07-28 20:09:46 UTC,2025-07-23 01:00:00 UTC,nodejs20.x,,256,180
mytrends-cl-uat-get-all-trends,2024-11-04 11:16:26 UTC,No disponible,nodejs20.x,,128,60
client-application-cl-dev-authentication-define-auth-challenge,2025-07-28 19:47:07 UTC,No disponible,nodejs20.x,,256,30
client-application-mx-rc-authentication-assign-role,2025-07-28 20:26:45 UTC,2025-07-22 23:00:00 UTC,nodejs20.x,,256,30
mytrends-mx-dev-set-new-suppliers-blocklisted,2024-10-29 20:06:14 UTC,No disponible,nodejs20.x,,128,30
client-application-cl-dev-authentication-verify-auth-challenge,2025-07-28 19:46:56 UTC,No disponible,nodejs20.x,,256,30
client-application-mx-uat-authentication-sync-user,2025-07-28 20:11:17 UTC,2025-07-29 17:00:00 UTC,nodejs20.x,,256,180
client-application-cl-dev-authentication-sync-user,2025-07-28 19:47:01 UTC,2025-07-29 11:00:00 UTC,nodejs20.x,,256,180
mytrends-mx-dev-send-email-on-trend-in-alert,2024-10-29 20:05:56 UTC,No disponible,nodejs20.x,,128,60
client-application-mx-dev-authentication-sync-user,2025-07-28 19:49:36 UTC,2025-07-29 17:00:00 UTC,nodejs20.x,,256,180
client-application-cl-uat-authentication-migrate-users,2025-07-28 20:09:35 UTC,2025-06-24 11:00:00 UTC,nodejs20.x,,256,900
client-application-cl-rc-authentication-check-sync-user,2025-07-28 20:24:02 UTC,No disponible,nodejs20.x,,256,900
client-application-cl-rc-apm-serverless-create-payment,2025-07-28 20:59:50 UTC,No disponible,nodejs20.x,,256,300
client-application-cl-uat-authentication-create-user,2025-07-28 20:09:40 UTC,No disponible,nodejs20.x,,256,300
client-application-cl-uat-authentication-upsert-user,2025-07-28 20:09:48 UTC,2025-05-08 17:00:00 UTC,nodejs20.x,,256,300
client-application-mx-uat-apm-serverless-create-payment,2025-07-28 20:54:27 UTC,No disponible,nodejs20.x,,256,300
client-application-cl-rc-authentication-migrate-users,2025-07-28 20:23:26 UTC,No disponible,nodejs20.x,,256,900
mytrends-cl-dev-get-all-trends,2024-10-29 20:05:51 UTC,No disponible,nodejs20.x,,128,60
client-application-mx-dev-documents-generate-pdf-react,2025-07-28 19:49:57 UTC,No disponible,nodejs18.x,,2048,300
mytrends-cl-dev-set-new-suppliers,2024-10-29 20:06:03 UTC,No disponible,nodejs20.x,,128,30
client-application-cl-dev-authentication-sync-user-event,2025-07-28 19:47:09 UTC,2025-07-29 11:00:00 UTC,nodejs20.x,,256,30
mytrends-cl-dev-set-new-suppliers-blocklisted,2024-10-29 20:06:34 UTC,No disponible,nodejs20.x,,128,30
client-application-cl-uat-apm-serverless-create-payment,2025-07-28 20:54:22 UTC,No disponible,nodejs20.x,,256,300
mytrends-cl-uat-remove-trends-without-alert,2024-11-04 11:16:38 UTC,No disponible,nodejs20.x,,128,30
mytrends-mx-dev-validate-trends-changed,2024-10-29 20:06:34 UTC,No disponible,nodejs20.x,,128,30
mytrends-cl-uat-set-new-suppliers,2024-11-04 11:17:09 UTC,No disponible,nodejs20.x,,128,30
client-application-mx-dev-authentication-sync-user-event,2025-07-28 19:49:43 UTC,2025-07-29 17:00:00 UTC,nodejs20.x,,256,30
mytrends-cl-dev-save-trends-in-alert,2024-10-29 20:06:09 UTC,No disponible,nodejs20.x,,128,30
client-application-mx-rc-authentication-remove-role,2025-07-28 20:26:32 UTC,2025-07-22 23:00:00 UTC,nodejs20.x,,256,30
client-application-cl-uat-notifications,2025-07-29 16:51:22 UTC,2025-07-29 19:00:00 UTC,nodejs20.x,,256,60
client-application-mx-uat-wallet-processors-transfers,2025-07-28 20:10:58 UTC,No disponible,nodejs20.x,,256,300
client-application-mx-uat-authentication-migrate-user,2025-07-28 20:11:15 UTC,2025-07-29 17:00:00 UTC,nodejs20.x,,256,300
mytrends-mx-uat-set-new-suppliers,2024-11-04 11:16:41 UTC,No disponible,nodejs20.x,,128,30
client-application-cl-rc-core-events-pipe-enrichment,2025-07-28 20:24:45 UTC,2025-07-29 15:00:00 UTC,nodejs20.x,,128,30
client-application-cl-dev-documents-generate-pdf-react,2025-07-28 19:47:14 UTC,No disponible,nodejs18.x,,2048,300
client-application-mx-uat-authentication-create-auth-challenge,2025-07-28 20:11:12 UTC,No disponible,nodejs20.x,,256,30
xepelin-pdf-generator-mx-dev-generate-pdf,2024-06-05 05:02:31 UTC,No disponible,nodejs18.x,,128,20
client-application-mx-uat-authentication-check-sync-user,2025-07-28 20:11:23 UTC,No disponible,nodejs20.x,,256,900
client-application-cl-rc-authentication-sync-user,2025-07-28 20:23:55 UTC,2025-07-28 15:00:00 UTC,nodejs20.x,,256,180
mytrends-cl-uat-process-page,2024-11-04 11:16:32 UTC,No disponible,nodejs20.x,,128,60
