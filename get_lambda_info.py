#!/usr/bin/env python3
"""
Script para obtener información de todas las funciones Lambda de una cuenta AWS.
Muestra: nombre, fecha de creación y fecha de última actividad.
"""

import boto3
import json
from datetime import datetime, timedelta
from botocore.exceptions import ClientError, NoCredentialsError
import argparse
import sys


class LambdaInfoCollector:
    def __init__(self, region_name='us-east-1'):
        """
        Inicializa el cliente de AWS Lambda y CloudWatch.
        
        Args:
            region_name (str): Región de AWS a consultar
        """
        try:
            self.lambda_client = boto3.client('lambda', region_name=region_name)
            self.cloudwatch_client = boto3.client('cloudwatch', region_name=region_name)
            self.region = region_name
        except NoCredentialsError:
            print("Error: No se encontraron credenciales de AWS.")
            print("Configura tus credenciales usando 'aws configure' o variables de entorno.")
            sys.exit(1)
    
    def get_all_lambda_functions(self):
        """
        Obtiene todas las funciones Lambda de la cuenta.
        
        Returns:
            list: Lista de funciones Lambda
        """
        functions = []
        paginator = self.lambda_client.get_paginator('list_functions')
        
        try:
            for page in paginator.paginate():
                functions.extend(page['Functions'])
        except ClientError as e:
            print(f"Error al obtener funciones Lambda: {e}")
            return []
        
        return functions
    
    def get_last_invocation_time(self, function_name):
        """
        Obtiene la fecha de la última invocación de una función Lambda.
        
        Args:
            function_name (str): Nombre de la función Lambda
            
        Returns:
            datetime or None: Fecha de última invocación o None si no hay datos
        """
        try:
            # Buscar métricas de invocación en los últimos 30 días
            end_time = datetime.utcnow()
            start_time = end_time - timedelta(days=30)
            
            response = self.cloudwatch_client.get_metric_statistics(
                Namespace='AWS/Lambda',
                MetricName='Invocations',
                Dimensions=[
                    {
                        'Name': 'FunctionName',
                        'Value': function_name
                    }
                ],
                StartTime=start_time,
                EndTime=end_time,
                Period=3600,  # 1 hora
                Statistics=['Sum']
            )
            
            # Encontrar la última invocación
            datapoints = response.get('Datapoints', [])
            if datapoints:
                # Filtrar solo los puntos con invocaciones > 0
                invocation_points = [dp for dp in datapoints if dp['Sum'] > 0]
                if invocation_points:
                    # Ordenar por timestamp y tomar el más reciente
                    latest = max(invocation_points, key=lambda x: x['Timestamp'])
                    return latest['Timestamp']
            
            return None
            
        except ClientError as e:
            print(f"Error al obtener métricas para {function_name}: {e}")
            return None
    
    def format_datetime(self, dt):
        """
        Formatea una fecha para mostrar.
        
        Args:
            dt (datetime): Fecha a formatear
            
        Returns:
            str: Fecha formateada
        """
        if dt is None:
            return "No disponible"
        
        # Convertir a timezone local si es necesario
        if hasattr(dt, 'replace'):
            return dt.strftime("%Y-%m-%d %H:%M:%S UTC")
        return str(dt)
    
    def collect_lambda_info(self):
        """
        Recopila información de todas las funciones Lambda.
        
        Returns:
            list: Lista de diccionarios con información de cada función
        """
        print(f"Obteniendo funciones Lambda en la región {self.region}...")
        functions = self.get_all_lambda_functions()
        
        if not functions:
            print("No se encontraron funciones Lambda.")
            return []
        
        print(f"Encontradas {len(functions)} funciones Lambda.")
        print("Obteniendo información de última actividad...")
        
        lambda_info = []
        
        for i, func in enumerate(functions, 1):
            function_name = func['FunctionName']
            print(f"Procesando {i}/{len(functions)}: {function_name}")
            
            # Obtener fecha de creación
            created_date = func.get('LastModified')
            if created_date:
                # LastModified está en formato ISO string
                created_date = datetime.fromisoformat(created_date.replace('Z', '+00:00'))
            
            # Obtener última invocación
            last_invocation = self.get_last_invocation_time(function_name)
            
            lambda_info.append({
                'name': function_name,
                'created_date': created_date,
                'last_invocation': last_invocation,
                'runtime': func.get('Runtime', 'N/A'),
                'description': func.get('Description', ''),
                'memory_size': func.get('MemorySize', 0),
                'timeout': func.get('Timeout', 0)
            })
        
        return lambda_info
    
    def print_lambda_info(self, lambda_info, output_format='table'):
        """
        Imprime la información de las funciones Lambda.
        
        Args:
            lambda_info (list): Lista con información de funciones
            output_format (str): Formato de salida ('table' o 'json')
        """
        if not lambda_info:
            print("No hay información para mostrar.")
            return
        
        if output_format == 'json':
            # Convertir datetime a string para JSON
            json_data = []
            for func in lambda_info:
                func_copy = func.copy()
                func_copy['created_date'] = self.format_datetime(func_copy['created_date'])
                func_copy['last_invocation'] = self.format_datetime(func_copy['last_invocation'])
                json_data.append(func_copy)
            
            print(json.dumps(json_data, indent=2, ensure_ascii=False))
        else:
            # Formato tabla
            print("\n" + "="*120)
            print(f"{'Nombre de la Función':<40} {'Fecha Creación':<25} {'Última Invocación':<25} {'Runtime':<15}")
            print("="*120)
            
            for func in lambda_info:
                name = func['name'][:39] if len(func['name']) > 39 else func['name']
                created = self.format_datetime(func['created_date'])[:24]
                last_inv = self.format_datetime(func['last_invocation'])[:24]
                runtime = func['runtime'][:14] if len(func['runtime']) > 14 else func['runtime']
                
                print(f"{name:<40} {created:<25} {last_inv:<25} {runtime:<15}")
            
            print("="*120)
            print(f"Total de funciones Lambda: {len(lambda_info)}")


def main():
    parser = argparse.ArgumentParser(
        description='Obtiene información de todas las funciones Lambda de una cuenta AWS'
    )
    parser.add_argument(
        '--region', 
        default='us-east-1',
        help='Región de AWS a consultar (default: us-east-1)'
    )
    parser.add_argument(
        '--format',
        choices=['table', 'json'],
        default='table',
        help='Formato de salida (default: table)'
    )
    
    args = parser.parse_args()
    
    # Crear el recolector de información
    collector = LambdaInfoCollector(region_name=args.region)
    
    # Recopilar información
    lambda_info = collector.collect_lambda_info()
    
    # Mostrar resultados
    collector.print_lambda_info(lambda_info, output_format=args.format)


if __name__ == "__main__":
    main()
