name,created_date,last_invocation,runtime,description,memory_size,timeout
COB-postgiro-cob-assignment-prepare-assignments,2025-05-06 22:07:16 UTC,No disponible,nodejs20.x,,256,240
order-service-prod-order-invoices-migration-CL,2025-06-03 21:42:44 UTC,No disponible,nodejs20.x,,2048,30
xepelin-kams-lambda-devel,2023-09-20 21:18:53 UTC,No disponible,nodejs18.x,,256,120
lending-payments-inbox-prod-dlq-storage-lambda,2023-07-17 19:22:24 UTC,2025-07-29 15:00:00 UTC,nodejs16.x,,128,3
cob-dev-outdated-debts-fn,2025-06-12 20:13:54 UTC,2025-07-29 15:00:00 UTC,nodejs20.x,,1024,900
requirements-dev-stack-createOrderRequirementsLamb-NUX64NWWfgu9,2024-12-04 15:11:41 UTC,No disponible,nodejs20.x,,128,90
order-invoice-enricher-mx-dev,2025-05-05 13:05:35 UTC,2025-07-24 17:00:00 UTC,nodejs20.x,,1024,60
dev-cob-activation-status-event-update-debt,2025-06-12 20:16:21 UTC,2025-07-25 17:00:00 UTC,nodejs20.x,,128,3
dev-cob-assignment-update-assignment,2025-02-13 12:58:15 UTC,No disponible,nodejs20.x,,128,60
lending-payments-inbox-prod-oxy-creditnote-update,2024-06-11 15:23:24 UTC,2025-07-29 21:00:00 UTC,nodejs20.x,,2048,30
cob-dev-calculate-order-invoices-debt-fn,2025-06-12 20:13:54 UTC,2025-07-29 21:00:00 UTC,nodejs20.x,,1024,900
document-update-generation-lambda-dev,2024-08-26 20:33:03 UTC,No disponible,nodejs18.x,,1024,120
dev-cob-update-debt-by-partial-pay-bb45431,2023-09-21 12:52:34 UTC,2025-05-12 15:00:00 UTC,nodejs18.x,,128,3
ar-requirements-events-de-updateRequirementVerifie-vfn7Q7M9dVDb,2025-05-05 13:04:57 UTC,No disponible,nodejs20.x,updateRequirementVerifiedAccountHandler-lambda-dev,128,30
COB-869-cob-update-debt-by-partial-pay,2023-09-11 20:54:31 UTC,2025-05-12 15:00:00 UTC,nodejs18.x,,128,180
cob-prod-send-order-invoices-to-calculate-debt-fn,2025-02-11 13:37:57 UTC,2025-07-29 07:00:00 UTC,nodejs20.x,,1024,900
order-service-rc-sg-origination-proxy-order-delete-MX,2024-10-17 15:20:25 UTC,No disponible,nodejs20.x,,2048,30
lending-payments-inbox-prod-mimetus-creditnote-update-v2,2024-06-11 15:23:24 UTC,2025-05-24 23:00:00 UTC,nodejs20.x,,2048,30
contacts-COB-1383-deleteBusinessContact,2025-02-11 17:30:36 UTC,No disponible,nodejs20.x,,1024,30
COB-testCM-cob-update-debt-by-partial-pay-af2250d,2023-09-21 13:30:14 UTC,2025-05-12 15:00:00 UTC,nodejs18.x,,128,3
sg-order-events-replay-prod-function,2024-08-26 23:54:35 UTC,No disponible,nodejs18.x,,1024,900
order-service-prod-order-invoices-migration-MX,2025-06-03 21:42:44 UTC,No disponible,nodejs20.x,,2048,30
prd-cob-notification-send-emails-lambda,2025-07-16 03:34:20 UTC,2025-07-29 21:00:00 UTC,nodejs20.x,,128,180
integration-monkey-prod-sftp-scraper,2023-01-23 19:06:08 UTC,2025-07-29 21:00:00 UTC,nodejs16.x,,1024,60
xepelin-authorizer-COB-1184-appsync-authorizer,2024-01-16 17:57:40 UTC,No disponible,nodejs18.x,,1024,5
prd-cob-debt-get-debts-by-identifier,2025-06-24 17:29:46 UTC,2025-07-29 17:00:00 UTC,nodejs20.x,,128,3
xepelin-authorizer-dev-appsync-authorizer,2025-06-12 20:13:41 UTC,2025-07-21 15:00:00 UTC,nodejs20.x,,1024,5
prd-cob-assignment-update-assignment,2025-02-13 19:25:40 UTC,2025-07-28 23:00:00 UTC,nodejs20.x,,128,60
order-service-rc-server-global-simulate-on-change-CL,2024-10-17 15:20:25 UTC,2025-07-29 15:00:00 UTC,nodejs20.x,,2048,30
debt-prod-debt-hard-collection-fn,2025-07-29 01:43:20 UTC,2025-07-29 17:00:00 UTC,nodejs20.x,,128,30
confirmation-get-analyst-lambda-dev,2024-07-11 18:18:59 UTC,2025-05-26 19:00:00 UTC,nodejs18.x,,256,120
COB-test-sns-cob-debt-get-debts-by-identifier,2025-03-19 13:23:45 UTC,No disponible,nodejs20.x,,128,3
order-service-dev-send-simulation-event-backoffice-MX,2025-06-12 20:16:20 UTC,No disponible,nodejs20.x,,2048,30
COB-test-sns-cob-notification-run-available-triggers-lambda,2025-03-19 13:26:11 UTC,No disponible,nodejs20.x,,256,3
prd-cob-debt-get-debt-order-invoices,2025-06-24 17:30:06 UTC,2025-07-29 21:00:00 UTC,nodejs20.x,,128,3
app-mvp-proxy-dev-server-global-query,2025-06-12 20:15:08 UTC,2025-07-29 19:00:00 UTC,nodejs20.x,,128,30
sg-order-ids-events-dev-function,2025-06-12 20:13:03 UTC,No disponible,nodejs20.x,,1024,900
COB-test-sns-cob-notification-send-emails-lambda,2025-03-19 16:41:10 UTC,No disponible,nodejs20.x,,128,180
contacts-prod-updateBusinessContact,2025-02-11 13:37:23 UTC,2025-07-29 21:00:00 UTC,nodejs20.x,,1024,30
lending-payments-inbox-prod-update-payment-item-status,2024-06-11 15:23:24 UTC,2025-07-28 13:00:00 UTC,nodejs20.x,,2048,15
prd-cob-assignment-prepare-assignments,2025-05-07 14:24:27 UTC,2025-07-28 23:00:00 UTC,nodejs20.x,,256,240
sg-business-events-replay-prod-function,2024-08-27 03:18:31 UTC,No disponible,nodejs18.x,,1024,900
order-service-rc-ops-cession-retriever,2024-10-17 15:20:25 UTC,2025-07-15 19:00:00 UTC,nodejs20.x,,2048,30
lending-payments-inbox-prod-mimetus-upsert,2024-06-11 15:23:24 UTC,No disponible,nodejs20.x,,2048,30
prd-cob-rates-update-event-update-debt,2025-06-12 20:22:25 UTC,2025-07-29 19:00:00 UTC,nodejs20.x,,128,3
debt-prod-deposit-created-fn,2025-07-29 01:43:37 UTC,2025-07-29 17:00:00 UTC,nodejs20.x,,128,180
dev-cob-debt-get-summary-products,2025-03-17 12:52:21 UTC,2025-07-02 23:00:00 UTC,nodejs20.x,,128,3
order-invoice-enricher-cl-prod,2024-11-12 22:10:13 UTC,2025-07-29 21:00:00 UTC,nodejs20.x,,1024,60
order-service-rc-oxy-update-creditnote,2024-10-17 15:20:25 UTC,No disponible,nodejs20.x,,2048,30
order-service-dev-origination-migration-consumer-MX,2025-06-12 20:16:19 UTC,2025-07-15 15:00:00 UTC,nodejs20.x,,2048,30
document-generation-lambda-dev,2024-08-26 20:32:55 UTC,No disponible,nodejs18.x,,1024,120
order-service-dev-sg-origination-proxy-ois-update-status-MX,2025-06-12 20:16:19 UTC,2025-07-23 21:00:00 UTC,nodejs20.x,,2048,30
ar-requirements-events-de-updateRequirementAssigna-pqUgbnzw8NWE,2025-05-05 13:04:57 UTC,No disponible,nodejs20.x,updateRequirementAssignableInvoice-lambda-dev,128,30
COB-test-sns-cob-rates-update-event-update-debt,2025-03-19 13:24:34 UTC,2025-05-26 15:00:00 UTC,nodejs20.x,,128,3
confirmation-processor-lambda-rc,2024-07-23 23:12:55 UTC,2025-07-22 23:00:00 UTC,nodejs18.x,,256,120
document-generation-orchestrator-lambda-dev,2024-08-02 21:53:12 UTC,No disponible,nodejs18.x,,1024,120
order-service-dev-origination-migration-producer-MX,2025-06-12 20:16:19 UTC,2025-07-14 19:00:00 UTC,nodejs20.x,,2048,900
debt-prod-debt-fn,2025-07-29 01:44:02 UTC,2025-07-29 17:00:00 UTC,nodejs20.x,,128,180
app-collections-core-prod-LogRetentionaae0aa3c5b4d-j4elfrpxDYZQ,2024-04-12 15:19:06 UTC,No disponible,nodejs18.x,,128,900
prd-cob-activation-status-event-update-debt,2025-06-12 20:22:20 UTC,2025-07-29 21:00:00 UTC,nodejs20.x,,128,3
confirmation-email-webhook-lambda-dev,2024-07-23 20:00:32 UTC,No disponible,nodejs18.x,,256,120
order-service-prod-ops-cession-retriever,2025-06-03 21:42:45 UTC,2025-07-29 21:00:00 UTC,nodejs20.x,,2048,30
requirements-dev-stack-getRequirementMetadataLambd-DgTmnHm6lO3T,2024-12-04 15:11:41 UTC,No disponible,nodejs20.x,,128,90
order-service-dev-stack-LogRetentionaae0aa3c5b4d4f-ogrjbNRDu5bj,2024-04-11 20:59:59 UTC,No disponible,nodejs18.x,,128,900
contacts-dev-getBusinessContacts,2025-06-12 20:14:38 UTC,2025-07-29 17:00:00 UTC,nodejs20.x,,1024,30
lending-payments-inbox-prod-server-global-order-update,2024-06-11 15:23:26 UTC,2025-07-29 21:00:00 UTC,nodejs20.x,,2048,30
debt-staging-payment-notifications-fn,2025-02-08 01:41:45 UTC,2025-07-21 23:00:00 UTC,nodejs20.x,,128,30
COB-postgiro-cob-partial-pay-cl-event-update-debt,2025-02-28 20:01:44 UTC,2025-07-29 19:00:00 UTC,nodejs18.x,,128,3
collection_cm_prepareEmails_cl_cob-1497-3,2024-08-07 16:45:18 UTC,No disponible,nodejs20.x,Example description,128,40
COB-test-sns-cob-notification-prepare-email-lambda,2025-03-19 13:28:49 UTC,No disponible,nodejs20.x,,256,600
lending-payments-inbox-prod-api-corps-update,2024-06-11 15:23:24 UTC,2025-07-09 17:00:00 UTC,nodejs20.x,,2048,30
server-global-sls-test-lambda-fn-order,2023-03-28 20:19:25 UTC,No disponible,nodejs16.x,,128,3
COB-test-sns-cob-partial-pay-event-update-debt,2025-03-19 13:23:29 UTC,2025-05-12 15:00:00 UTC,nodejs20.x,,128,3
contacts-dev-deleteBusinessContact,2025-06-12 20:14:37 UTC,No disponible,nodejs20.x,,1024,30
dev-cob-debt-get-debt-order-invoices,2025-03-17 12:52:27 UTC,2025-07-29 15:00:00 UTC,nodejs20.x,,128,3
datadog-forwarder-v3,2024-02-06 18:36:13 UTC,2025-07-29 21:00:00 UTC,python3.11,"Pushes logs, metrics and traces from AWS to Datadog.",1024,120
shared-authorizer-lambda-prod,2024-06-21 17:42:18 UTC,2025-07-29 21:00:00 UTC,nodejs18.x,,256,120
requirements-dev-stack-getExceptionByIdLambdaDB674-JHTWS34wo1nK,2024-12-04 15:11:41 UTC,No disponible,nodejs20.x,,128,90
CheckDocumentsSignExpirat-checkDocumentsSignExpira-0TU9or5jgs4f,2025-05-05 13:04:31 UTC,2025-07-29 15:00:00 UTC,nodejs20.x,,128,300
debt-prod-rate-event-forwarding-fn,2025-07-29 01:44:18 UTC,2025-07-29 19:00:00 UTC,nodejs20.x,,128,30
sg-sat-paids-events-replay-test-sf2-function,2024-08-27 03:01:37 UTC,No disponible,nodejs18.x,,1024,900
lending-payments-inbox-prod-server-global-business-upsert,2024-06-11 15:23:24 UTC,2025-07-29 21:00:00 UTC,nodejs20.x,,2048,30
app-mvp-proxy-prod-server-global-query,2024-03-25 18:19:04 UTC,2025-07-29 21:00:00 UTC,nodejs18.x,,128,30
notify-xml-invoice-lambda-dev,2024-01-25 16:57:31 UTC,No disponible,nodejs18.x,,256,120
requirements-dev-stack-updateOrderRequirementsLamb-WxecgQa9SHYq,2024-12-04 15:11:41 UTC,No disponible,nodejs20.x,,128,90
confirmation-processor-lambda-prod,2024-07-24 19:39:32 UTC,No disponible,nodejs18.x,,256,120
dev-cob-notification-prepare-email-lambda,2025-06-12 20:16:33 UTC,No disponible,nodejs20.x,,256,600
integration-monkey-dev-sftp-scraper,2023-01-23 18:55:31 UTC,No disponible,nodejs16.x,,1024,60
cob-COB-1383-send-order-invoices-to-calculate-debt-fn,2025-02-10 18:25:32 UTC,No disponible,nodejs20.x,,1024,900
requirements-dev-stack-regularizeExceptionLambda1E-G6MRaI62IKE1,2024-12-04 15:11:41 UTC,No disponible,nodejs20.x,,128,90
contacts-COB-1383-createBusinessContact,2025-02-11 17:30:35 UTC,No disponible,nodejs20.x,,1024,30
xepelin-authorizer-COB-1349-appsync-authorizer,2024-04-29 15:47:12 UTC,No disponible,nodejs20.x,,1024,5
order-service-dev-origination-migration-producer-CL,2025-06-12 20:16:19 UTC,2025-07-14 19:00:00 UTC,nodejs20.x,,2048,900
test-ops-v2-migration-ops-migration-test-lambda-fn,2023-09-01 14:15:21 UTC,No disponible,nodejs16.x,,128,180
order-service-rc-server-global-simulate-on-change-MX,2024-10-17 15:20:25 UTC,2025-07-22 17:00:00 UTC,nodejs20.x,,2048,30
order-service-rc-stack-LogRetentionaae0aa3c5b4d4f8-FCyzEauKW4sZ,2024-10-17 15:20:24 UTC,No disponible,nodejs18.x,,128,900
prd-cob-debt-get-payers,2025-06-24 17:30:41 UTC,2025-07-29 19:00:00 UTC,nodejs20.x,,128,30
confirmation-email-webhook-lambda-rc,2024-07-23 19:58:13 UTC,No disponible,nodejs18.x,,256,120
dev-cob-notification-send-emails-lambda,2025-05-07 14:23:37 UTC,No disponible,nodejs20.x,,128,180
order-service-dev-sg-origination-proxy-ois-update-status-CL,2025-06-12 20:16:20 UTC,2025-07-29 19:00:00 UTC,nodejs20.x,,2048,30
requirements-dev-stack-softDeleteExceptionLambdaF5-L0CqgC0Eb2up,2024-12-04 15:11:41 UTC,No disponible,nodejs20.x,,128,90
contacts-prod-markToSendEmailContact,2025-02-11 13:37:22 UTC,2025-07-29 21:00:00 UTC,nodejs20.x,,1024,30
requirements-dev-stack-updateInvoiceRequirementsLa-ioooaWF7iho9,2024-12-04 15:11:41 UTC,No disponible,nodejs20.x,,128,90
xepelin-authorizer-COB-1383-appsync-authorizer,2024-10-03 14:05:03 UTC,No disponible,nodejs20.x,,1024,5
COB-test-sns-cob-assignment-get-collector,2025-03-19 13:24:51 UTC,No disponible,nodejs20.x,,128,3
COB-postgiro-cob-rates-update-event-update-debt,2025-02-28 20:02:06 UTC,2025-05-26 15:00:00 UTC,nodejs20.x,,128,3
contacts-dev-getBusinessMasterContacts,2025-06-12 20:14:38 UTC,2025-05-26 19:00:00 UTC,nodejs20.x,,1024,30
app-collections-core-dev--LogRetentionaae0aa3c5b4d-5FHk3UONQkoC,2024-04-11 20:52:19 UTC,No disponible,nodejs18.x,,128,900
lending-payments-inbox-prod-data-team-update-key,2024-06-11 15:23:24 UTC,No disponible,nodejs20.x,,2048,30
contacts-prod-getBusinessMasterContacts,2025-02-11 13:37:22 UTC,2025-07-29 19:00:00 UTC,nodejs20.x,,1024,30
contacts-COB-1383-getBusinessContacts,2025-02-11 17:30:35 UTC,No disponible,nodejs20.x,,1024,30
CheckDocumentsSignExpirat-checkDocumentsSignExpira-0r5xsQOTvELb,2025-02-10 18:24:24 UTC,2025-07-29 17:00:00 UTC,nodejs20.x,,128,300
prd-cob-notification-run-available-triggers-lambda,2025-03-17 12:50:00 UTC,2025-07-29 13:00:00 UTC,nodejs20.x,,256,3
prd-cob-partial-pay-cl-event-update-debt,2025-06-12 20:22:37 UTC,2025-07-29 19:00:00 UTC,nodejs18.x,,128,3
order-enricher-dlq-alarm-fn-cl-prod,2024-11-12 22:10:07 UTC,No disponible,nodejs16.x,,128,3
contacts-prod-createBusinessContact,2025-02-11 13:37:22 UTC,2025-07-29 21:00:00 UTC,nodejs20.x,,1024,30
COB-test-sns-cob-assignment-update-assignment,2025-03-19 13:25:50 UTC,No disponible,nodejs20.x,,128,60
AutomationStateOrderStack-automationNotifyRequirem-qoijKrXs5OUQ,2025-05-05 13:05:23 UTC,No disponible,nodejs20.x,,128,30
order-service-dev-ops-cession-retriever,2025-06-12 20:16:20 UTC,2025-07-15 19:00:00 UTC,nodejs20.x,,2048,30
COB-testCM-cob-web-hook-notification-0f8b4aa,2023-09-21 12:21:42 UTC,No disponible,nodejs18.x,,128,3
confirmation-update-analyst-lambda-dev,2024-07-23 20:00:25 UTC,No disponible,nodejs18.x,,256,120
requirements-dev-stack-getInvoiceRequirementsLambd-lJPU9Hvgzkjd,2024-12-04 15:11:41 UTC,No disponible,nodejs20.x,,128,90
lending-payments-inbox-prod-oxy-upsert,2024-06-11 15:23:24 UTC,2025-07-29 21:00:00 UTC,nodejs20.x,,2048,30
order-invoice-enricher-cl-dev,2025-05-05 13:06:12 UTC,2025-07-29 19:00:00 UTC,nodejs20.x,,1024,60
sg-order-events-replay-test-function,2024-08-27 00:55:05 UTC,No disponible,nodejs18.x,,1024,900
dev-cob-partial-pay-event-update-debt,2025-06-12 20:16:15 UTC,2025-05-12 15:00:00 UTC,nodejs20.x,,128,3
order-service-rc-auto-update-issued-date-CL,2024-10-17 15:20:25 UTC,2025-07-29 03:00:00 UTC,nodejs20.x,,2048,900
order-service-rc-mimetus-update-payment,2024-10-17 15:20:25 UTC,No disponible,nodejs20.x,,2048,30
kam-assignment-registry-lambda-stg,2024-08-01 20:35:57 UTC,No disponible,nodejs18.x,,512,120
cm-prod-manual-hard-collection-fn,2025-06-30 19:43:53 UTC,2025-06-30 19:00:00 UTC,nodejs20.x,,128,30
COB-test-sns-cob-activation-status-event-update-debt,2025-03-19 13:23:37 UTC,2025-07-25 17:00:00 UTC,nodejs20.x,,128,3
sg-order-ids-events-prod-function,2024-08-27 03:11:49 UTC,No disponible,nodejs18.x,,1024,900
COB-test-sns-cob-debt-get-payers,2025-03-19 13:24:18 UTC,No disponible,nodejs20.x,,128,30
lending-payments-inbox-prod-mimetus-upsert-v2,2024-06-11 15:23:24 UTC,2025-06-02 17:00:00 UTC,nodejs20.x,,2048,60
sg-order-ids-events-test-sf2-function,2024-08-27 01:33:44 UTC,No disponible,nodejs18.x,,1024,900
lending-payments-inbox-prod-logging-alert-fn,2024-04-10 18:28:32 UTC,2025-07-15 21:00:00 UTC,nodejs16.x,,128,3
COB-postgiro-cob-debt-get-debts-by-credit-line,2025-03-18 15:08:53 UTC,No disponible,nodejs20.x,,128,3
sg-sat-paids-events-replay-prod-function,2024-08-27 02:57:17 UTC,No disponible,nodejs18.x,,1024,900
debt-staging-send-daily-process-messages-trigger-fn,2025-02-03 14:30:30 UTC,2025-07-29 03:00:00 UTC,nodejs20.x,,128,180
cob-COB-1383-calculate-order-invoices-debt-fn,2025-02-10 18:25:32 UTC,No disponible,nodejs20.x,,1024,900
requirements-dev-stack-getExceptionsByRequirementL-iuVFBnvq69f5,2024-12-04 15:11:41 UTC,No disponible,nodejs20.x,,128,90
lending-payments-inbox-prod-dlq-alarm-lambda,2024-04-10 18:28:32 UTC,2025-05-16 17:00:00 UTC,nodejs16.x,,128,3
dev-cob-assignment-get-collector,2025-02-13 12:57:51 UTC,No disponible,nodejs20.x,,128,3
dev-cob-monitoring-sqs-monitor,2025-02-13 12:57:57 UTC,No disponible,nodejs20.x,,128,10
debt-staging-payer-contribution-sync-trigger-fn,2025-01-08 05:29:47 UTC,2025-07-29 11:00:00 UTC,nodejs20.x,,128,900
notify-xml-invoice-lambda-prod,2024-05-29 21:56:58 UTC,2025-06-02 17:00:00 UTC,nodejs18.x,,512,120
contacts-dev-createBusinessContact,2025-06-12 20:14:38 UTC,No disponible,nodejs20.x,,1024,30
app-mvp-proxy-test-server-global-query,2023-06-09 21:00:59 UTC,No disponible,nodejs18.x,,128,30
sg-business-events-replay-test-sf2-function,2024-08-27 03:08:24 UTC,No disponible,nodejs18.x,,1024,900
xepelin-authorizer-COB-postgiro-appsync-authorizer,2025-02-11 17:30:35 UTC,No disponible,nodejs20.x,,1024,5
contacts-dev-markToSendEmailContact,2025-06-12 20:14:38 UTC,No disponible,nodejs20.x,,1024,30
ar-requirements-events-de-updateRequirementBankAcc-o8LmmtrQmZZ7,2025-05-05 13:04:57 UTC,2025-07-29 19:00:00 UTC,nodejs20.x,updateRequirementBankAccountHandler-lambda-dev,128,30
rodrigo-test,2023-08-26 15:02:21 UTC,No disponible,nodejs18.x,,128,3
order-service-prod-sg-origination-proxy-ois-update-status-MX,2025-06-03 21:42:44 UTC,2025-07-29 21:00:00 UTC,nodejs20.x,,2048,30
requirements-dev-stack-validateTokenLambdaB4E5E20C-tPOzEbWMJRQJ,2024-12-04 15:11:53 UTC,No disponible,nodejs20.x,,128,90
order-service-rc-logging-alert-fn,2024-10-17 15:20:19 UTC,2025-07-29 15:00:00 UTC,nodejs16.x,,128,3
xepelin-authorizer-test-appsync-authorizer,2023-03-31 13:21:01 UTC,No disponible,nodejs18.x,,1024,5
requirements-dev-stack-updateExceptionLambdaBB1BA1-vIK4FCt4aVsN,2024-12-04 15:11:41 UTC,No disponible,nodejs20.x,,128,90
ar-requirements-events-de-updateRequirementCededIn-dROTdrYh5fUJ,2025-05-05 13:04:57 UTC,2025-07-15 19:00:00 UTC,nodejs20.x,updateRequirementCededInvoiceHandler-lambda-dev,128,30
dev-cob-rates-update-event-update-debt,2025-06-12 20:16:10 UTC,2025-05-26 15:00:00 UTC,nodejs20.x,,128,3
confirmation-processor-lambda-dev,2024-07-31 23:13:16 UTC,No disponible,nodejs18.x,,256,120
collection_collection-debts_prepareEmails_cl_COB-1497,2024-08-06 01:17:43 UTC,No disponible,nodejs20.x,prepare emails to be send,128,40
debt-prod-payment-notifications-fn,2025-07-29 01:43:54 UTC,2025-07-29 17:00:00 UTC,nodejs20.x,,128,30
AutomationStateOrderStack-automationStateOrderHand-oV13Oork5tiK,2025-05-05 13:05:11 UTC,No disponible,nodejs20.x,,128,30
contacts-prod-stack-LogRetentionaae0aa3c5b4d4f87b0-W8FR5sY5B2XE,2024-04-12 14:42:49 UTC,No disponible,nodejs18.x,,128,900
order-service-dev-logging-alert-fn,2025-05-05 13:08:26 UTC,2025-07-23 19:00:00 UTC,nodejs16.x,,128,3
order-enricher-stack-mx-d-LogRetentionaae0aa3c5b4d-FcGEtNMQEVRT,2024-04-11 20:50:04 UTC,No disponible,nodejs18.x,,128,900
COB-postgiro-cob-notification-run-available-triggers-lambda,2025-03-18 15:09:11 UTC,2025-05-08 19:00:00 UTC,nodejs20.x,,256,3
order-service-prod-logging-alert-fn,2025-06-03 21:42:44 UTC,2025-07-29 21:00:00 UTC,nodejs16.x,,128,3
confirmation-get-analyst-lambda-rc,2024-07-23 17:07:07 UTC,2025-05-26 15:00:00 UTC,nodejs18.x,,256,120
prd-cob-notification-prepare-email-lambda,2025-06-24 17:31:09 UTC,2025-06-25 21:00:00 UTC,nodejs20.x,,256,600
contacts-dev-stack-LogRetentionaae0aa3c5b4d4f87b02-RSt8F5tWiidA,2024-04-11 20:53:08 UTC,No disponible,nodejs18.x,,128,900
order-enricher-dlq-alarm-fn-cl-dev,2025-05-05 13:06:11 UTC,No disponible,nodejs16.x,,128,3
order-enricher-dlq-alarm-fn-mx-dev,2025-05-05 13:05:34 UTC,2025-07-23 01:00:00 UTC,nodejs16.x,,128,3
requirements-dev-stack-createExceptionsLambda5E266-tElxnybBJxkw,2024-12-04 15:11:41 UTC,No disponible,nodejs20.x,,128,90
cob-prod-calculate-order-invoices-debt-fn,2025-02-11 13:37:57 UTC,2025-07-29 21:00:00 UTC,nodejs20.x,,1024,900
prd-cob-debt-get-summary-products,2025-06-24 17:31:19 UTC,2025-07-29 19:00:00 UTC,nodejs20.x,,128,3
contacts-prod-createContacts,2025-02-11 13:37:22 UTC,No disponible,nodejs20.x,,1024,30
dev-cob-debt-get-debts-by-credit-line,2025-03-17 12:52:32 UTC,No disponible,nodejs20.x,,128,3
COB-postgiro-cob-debt-update-hard-collections,2025-05-06 22:07:31 UTC,2025-06-02 21:00:00 UTC,nodejs20.x,,128,60
COB-test-sns-cob-debt-get-debt-order-invoices,2025-03-19 13:23:53 UTC,No disponible,nodejs20.x,,128,3
order-service-rc-oxy-claim-invoice,2024-10-17 15:20:59 UTC,No disponible,nodejs20.x,,2048,30
COB-postgiro-cob-assignment-get-collector,2025-02-11 17:34:50 UTC,2025-06-05 19:00:00 UTC,nodejs20.x,,128,3
requirements-dev-stack-deleteOrderRequirementsLamb-rXtL2xSblWHE,2024-12-04 15:11:41 UTC,No disponible,nodejs20.x,,128,90
dev-cob-assignment-prepare-assignments,2025-05-07 14:23:31 UTC,No disponible,nodejs20.x,,256,240
order-enricher-dlq-alarm-fn-mx-prod,2024-11-12 22:09:21 UTC,2025-06-12 19:00:00 UTC,nodejs16.x,,128,3
cob-dev-send-order-invoices-to-calculate-debt-fn,2025-06-12 20:13:54 UTC,2025-07-29 05:00:00 UTC,nodejs20.x,,1024,900
order-service-rc-order-invoices-migration-CL,2024-10-17 15:20:25 UTC,No disponible,nodejs20.x,,2048,30
order-service-rc-mimetus-cancel-invoice,2024-10-17 15:20:59 UTC,No disponible,nodejs20.x,,2048,30
shared-authorizer-lambda-dev,2024-08-01 20:35:45 UTC,2025-07-22 11:00:00 UTC,nodejs18.x,,256,120
contacts-COB-1383-markToSendEmailContact,2025-02-11 17:30:35 UTC,No disponible,nodejs20.x,,1024,30
download-xml-invoice-lambda-stg,2024-01-25 16:53:47 UTC,No disponible,nodejs18.x,,256,120
dev-cob-debt-update-hard-collections,2025-04-23 19:52:57 UTC,No disponible,nodejs20.x,,128,60
requirements-dev-stack-getOrderRequirementsLambda9-OdYe9aqoo4iB,2024-12-04 15:11:41 UTC,No disponible,nodejs20.x,,128,90
order-service-prod-origination-migration-consumer-MX,2025-06-03 21:42:44 UTC,2025-07-25 21:00:00 UTC,nodejs20.x,,2048,30
COB-postgiro-cob-activation-status-event-update-debt,2025-02-28 20:01:52 UTC,2025-07-25 17:00:00 UTC,nodejs20.x,,128,3
requirements-dev-stack-deleteInvoiceRequirementsLa-ZM4pMRredfJo,2024-12-04 15:11:41 UTC,No disponible,nodejs20.x,,128,90
order-service-rc-send-simulation-event-backoffice-CL,2024-10-17 15:20:25 UTC,2025-07-29 15:00:00 UTC,nodejs20.x,,2048,30
app-mvp-proxy-stack-test-LogRetentionaae0aa3c5b4d4-aZb4dP8I4wQU,2023-06-07 15:15:30 UTC,No disponible,nodejs14.x,,128,3
xepelin-kams-lambda-prod,2023-11-17 00:38:54 UTC,2025-07-29 21:00:00 UTC,nodejs18.x,,256,120
COB-postgiro-cob-debt-get-debt-order-invoices,2025-03-18 15:08:46 UTC,No disponible,nodejs20.x,,128,3
debt-staging-rate-event-forwarding-fn,2025-01-08 05:30:38 UTC,2025-05-26 15:00:00 UTC,nodejs20.x,,128,30
order-service-rc-sg-origination-proxy-order-delete-CL,2024-10-17 15:20:25 UTC,No disponible,nodejs20.x,,2048,30
debt-staging-debt-hard-collection-fn,2025-03-19 13:48:20 UTC,2025-07-29 19:00:00 UTC,nodejs20.x,,128,30
debt-staging-debt-fn,2025-05-13 21:53:51 UTC,2025-07-27 03:00:00 UTC,nodejs20.x,,128,180
confirmation-update-analyst-lambda-prod,2024-07-24 19:39:14 UTC,No disponible,nodejs18.x,,256,120
order-service-prod-send-simulation-event-backoffice-MX,2025-06-03 21:42:44 UTC,No disponible,nodejs20.x,,2048,30
contacts-prod-getBusinessContacts,2025-02-11 13:37:22 UTC,2025-07-29 21:00:00 UTC,nodejs20.x,,1024,30
order-service-dev-order-invoices-migration-CL,2025-06-12 20:16:20 UTC,No disponible,nodejs20.x,,2048,30
app-collections-core-COB--LogRetentionaae0aa3c5b4d-8PXJEOldcJUK,2025-02-10 18:25:31 UTC,No disponible,nodejs18.x,,128,900
datadog-forwarder-v2,2024-02-05 19:09:18 UTC,2025-07-29 21:00:00 UTC,python3.11,"Pushes logs, metrics and traces from AWS to Datadog.",1024,120
order-service-prod-stack-LogRetentionaae0aa3c5b4d4-xv9KPPxRbjUH,2024-04-19 19:54:22 UTC,No disponible,nodejs18.x,,128,900
cm-staging-manual-hard-collection-fn,2025-06-26 21:45:47 UTC,2025-06-06 13:00:00 UTC,nodejs20.x,,128,30
cob-COB-1383-outdated-debts-fn,2025-02-10 18:25:32 UTC,No disponible,nodejs20.x,,1024,900
order-service-dev-order-invoices-migration-MX,2025-06-12 20:16:19 UTC,No disponible,nodejs20.x,,2048,30
order-service-rc-sg-origination-proxy-ois-update-status-CL,2024-10-17 15:20:25 UTC,2025-07-29 15:00:00 UTC,nodejs20.x,,2048,30
sg-order-ids-events-test-function,2024-08-26 23:17:46 UTC,No disponible,nodejs18.x,,1024,900
COB-postgiro-cob-notification-prepare-email-lambda,2025-06-02 21:10:26 UTC,2025-05-08 19:00:00 UTC,nodejs20.x,,256,600
sg-order-events-replay-dev-function,2025-06-12 20:13:02 UTC,No disponible,nodejs20.x,,1024,900
staging-auto-update-issued-date-lambda,2024-01-04 18:39:45 UTC,No disponible,nodejs16.x,,128,180
xepelin-authorizer-undefined-appsync-authorizer,2025-02-10 18:25:32 UTC,No disponible,nodejs20.x,,1024,5
document-rule-engine-lambda-dev,2024-08-26 20:32:48 UTC,No disponible,nodejs18.x,,1024,120
datadog-forwarder,2024-02-05 18:34:01 UTC,2025-07-29 21:00:00 UTC,python3.9,"Pushes logs, metrics and traces from AWS to Datadog.",1024,120
lending-payments-inbox-prod-update-provider-is-highlighted,2024-06-11 15:23:24 UTC,2025-07-22 19:00:00 UTC,nodejs20.x,,2048,15
COB-postgiro-cob-assignment-update-assignment,2025-02-11 17:34:57 UTC,No disponible,nodejs20.x,,128,60
contacts-COB-1383-updateBusinessContact,2025-02-11 17:30:35 UTC,No disponible,nodejs20.x,,1024,30
COB-test-sns-cob-monitoring-sqs-monitor,2025-03-19 13:24:43 UTC,No disponible,nodejs20.x,,128,10
prd-cob-debt-get-debts-by-credit-line,2025-06-24 17:31:29 UTC,No disponible,nodejs20.x,,128,3
COB-test-sns-cob-debt-get-debts-by-credit-line,2025-03-19 13:24:01 UTC,No disponible,nodejs20.x,,128,3
confirmation-email-webhook-lambda-prod,2024-07-24 19:39:21 UTC,No disponible,nodejs18.x,,256,120
requirements-dev-stack-createInvoiceRequirementsLa-2Mtsag9fh0TO,2024-12-04 15:11:41 UTC,No disponible,nodejs20.x,,128,90
order-service-prod-origination-migration-producer-MX,2025-06-03 21:42:44 UTC,2025-07-25 19:00:00 UTC,nodejs20.x,,2048,900
order-service-dev-origination-migration-consumer-CL,2025-06-12 20:16:19 UTC,2025-07-14 23:00:00 UTC,nodejs20.x,,2048,30
order-enricher-stack-cl-p-LogRetentionaae0aa3c5b4d-rDVo0qlBhkVv,2024-11-12 22:10:12 UTC,No disponible,nodejs18.x,,128,900
contacts-COB-1383-getBusinessMasterContacts,2025-02-11 17:30:35 UTC,No disponible,nodejs20.x,,1024,30
dev-cob-debt-get-payers,2024-11-29 14:01:44 UTC,2025-07-29 15:00:00 UTC,nodejs20.x,,128,30
order-enricher-stack-mx-p-LogRetentionaae0aa3c5b4d-x88MvguOzoYH,2024-11-12 22:09:27 UTC,No disponible,nodejs18.x,,128,900
order-service-prod-origination-migration-producer-CL,2025-06-03 21:42:44 UTC,2025-07-25 19:00:00 UTC,nodejs20.x,,2048,900
notify-xml-invoice-lambda-stg,2024-01-25 16:54:42 UTC,No disponible,nodejs18.x,,256,120
lending-payments-inbox-prod-get-payments-items,2024-06-11 15:23:24 UTC,No disponible,nodejs20.x,,2048,15
COB-postgiro-cob-notification-send-emails-lambda,2025-06-02 21:09:40 UTC,2025-05-09 19:00:00 UTC,nodejs20.x,,128,180
confirmation-update-analyst-lambda-rc,2024-07-23 19:58:07 UTC,No disponible,nodejs18.x,,256,120
ar-requirements-events-de-updateRequirementRejecte-TuDyojYkT0s9,2025-05-05 13:04:57 UTC,2025-07-29 19:00:00 UTC,nodejs20.x,updateRequirementRejectedInvoiceHandler-lambda-dev,128,30
confirmation-get-analyst-lambda-prod,2024-07-12 19:14:55 UTC,2025-06-05 17:00:00 UTC,nodejs18.x,,256,120
requirements-dev-stack-updateIsLockedOrderRequirem-UvkGJx8Dua57,2024-12-04 15:11:41 UTC,No disponible,nodejs20.x,,128,90
dev-cob-notification-run-available-triggers-lambda,2025-03-17 12:52:38 UTC,No disponible,nodejs20.x,,256,3
extraction-xml-invoice-lambda-stg,2024-01-25 16:54:49 UTC,No disponible,nodejs18.x,,256,120
debt-staging-deposit-created-fn,2025-04-29 19:40:07 UTC,2025-07-21 23:00:00 UTC,nodejs20.x,,128,180
app-mvp-proxy-stack-dev-LogRetentionaae0aa3c5b4d4f-oXgyut5yJBfG,2024-04-11 20:54:17 UTC,No disponible,nodejs18.x,,128,900
order-service-rc-send-simulation-event-backoffice-MX,2024-10-17 15:20:25 UTC,2025-07-22 15:00:00 UTC,nodejs20.x,,2048,30
COB-postgiro-cob-partial-pay-event-update-debt,2025-02-28 20:01:59 UTC,2025-05-12 15:00:00 UTC,nodejs20.x,,128,3
order-service-prod-sg-origination-proxy-ois-update-status-CL,2025-06-03 21:42:44 UTC,2025-07-29 21:00:00 UTC,nodejs20.x,,2048,30
extraction-xml-invoice-lambda-dev,2024-01-25 18:16:03 UTC,2025-07-22 17:00:00 UTC,nodejs18.x,,256,120
extraction-xml-invoice-lambda-prod,2024-05-29 21:57:11 UTC,2025-07-29 21:00:00 UTC,nodejs18.x,,512,120
order-service-dev-send-simulation-event-backoffice-CL,2025-06-12 20:16:19 UTC,No disponible,nodejs20.x,,2048,30
COB-testCM-cob-get-debt-order-invoices-d87a21a,2023-09-21 12:50:41 UTC,No disponible,nodejs18.x,,128,3
contacts-prod-deleteBusinessContact,2025-02-11 13:37:22 UTC,2025-07-29 19:00:00 UTC,nodejs20.x,,1024,30
CdkRemixAppStack-EdgeFn92E6D7FF-xoi4UQi7xrOv,2023-06-07 01:19:11 UTC,No disponible,nodejs18.x,,1024,10
requirements-dev-stack-getInvoiceRequirementsByOrd-X6Kg3oUx6msG,2024-12-04 15:11:41 UTC,No disponible,nodejs20.x,,128,90
lending-payments-inbox-prod-mimetus-payment-update-v2,2024-06-11 15:23:24 UTC,2025-05-24 15:00:00 UTC,nodejs20.x,,2048,30
contacts-COB-1383-stack-LogRetentionaae0aa3c5b4d4f-r5t0qy5QIARx,2025-02-11 17:30:33 UTC,No disponible,nodejs18.x,,128,900
xepelin-authorizer-DD-1485-appsync-authorizer,2024-07-04 12:59:23 UTC,No disponible,nodejs20.x,,1024,5
cob-prod-outdated-debts-fn,2025-02-11 13:37:57 UTC,2025-07-29 15:00:00 UTC,nodejs20.x,,1024,900
COB-test-sns-cob-debt-update-hard-collections,2025-03-19 13:25:42 UTC,2025-07-29 21:00:00 UTC,nodejs20.x,,128,60
CheckDocumentsSignExpirat-checkDocumentsSignExpira-PX3EC7STrKCo,2025-03-24 21:11:29 UTC,2025-07-29 17:00:00 UTC,nodejs20.x,,128,300
lending-payments-inbox-prod-mimetus-creditnote-update,2024-06-11 15:23:24 UTC,No disponible,nodejs20.x,,2048,30
COB-postgiro-cob-debt-get-payers,2025-01-28 04:25:36 UTC,No disponible,nodejs20.x,,128,30
sg-sat-paids-events-replay-dev-function,2025-06-12 20:13:03 UTC,No disponible,nodejs20.x,,1024,900
COB-test-sns-cob-debt-get-summary-products,2025-03-19 13:24:10 UTC,No disponible,nodejs20.x,,128,3
contacts-COB-1383-createContacts,2025-02-11 17:30:37 UTC,No disponible,nodejs20.x,,1024,30
lending-payments-inbox-prod-mimetus-payment-update,2024-06-11 15:23:24 UTC,No disponible,nodejs20.x,,2048,30
order-service-rc-sg-origination-proxy-ois-update-status-MX,2024-10-17 15:20:25 UTC,2025-07-22 17:00:00 UTC,nodejs20.x,,2048,30
prd-cob-debt-update-hard-collections,2025-06-24 17:31:40 UTC,2025-07-29 21:00:00 UTC,nodejs20.x,,128,60
COB-postgiro-cob-debt-get-debts-by-identifier,2025-03-18 15:08:36 UTC,No disponible,nodejs20.x,,128,3
sg-order-events-replay-test-sf2-function,2024-08-27 02:04:12 UTC,No disponible,nodejs18.x,,1024,900
download-xml-invoice-lambda-prod,2024-05-29 21:57:05 UTC,2025-06-02 17:00:00 UTC,nodejs18.x,,512,120
order-service-prod-send-simulation-event-backoffice-CL,2025-06-03 21:42:44 UTC,No disponible,nodejs20.x,,2048,30
debt-staging-update-expired-trigger-fn,2025-07-09 22:11:01 UTC,2025-07-29 05:00:00 UTC,nodejs20.x,,128,180
prd-cob-monitoring-sqs-monitor,2025-02-13 19:25:10 UTC,2025-07-29 19:00:00 UTC,nodejs20.x,,128,10
xepelin-authorizer-CarlosMario-appsync-authorizer,2024-01-15 20:35:50 UTC,No disponible,nodejs18.x,,1024,5
order-enricher-stack-cl-d-LogRetentionaae0aa3c5b4d-pwZEJGaafoDS,2024-04-15 22:05:16 UTC,No disponible,nodejs18.x,,128,900
app-mvp-proxy-stack-prod-LogRetentionaae0aa3c5b4d4-qR5rJAwb3FNS,2023-08-03 19:01:37 UTC,No disponible,nodejs16.x,,128,3
sg-business-events-replay-test-function,2024-08-27 01:24:03 UTC,No disponible,nodejs18.x,,1024,900
requirements-dev-stack-filterOrderRequirementsLamb-x69ULel9Is1E,2024-12-04 15:11:41 UTC,No disponible,nodejs20.x,,128,90
COB-test-sns-cob-partial-pay-cl-event-update-debt,2025-03-19 13:24:26 UTC,2025-05-12 15:00:00 UTC,nodejs18.x,,128,3
lending-payments-inbox-pr-LogRetentionaae0aa3c5b4d-o15AiyIaBZs7,2024-05-06 21:35:39 UTC,No disponible,nodejs18.x,,128,900
dev-cob-partial-pay-cl-event-update-debt,2025-06-12 20:16:27 UTC,2025-07-29 13:00:00 UTC,nodejs18.x,,128,3
dev-cob-debt-get-debts-by-identifier,2025-03-17 12:52:15 UTC,2025-07-29 15:00:00 UTC,nodejs20.x,,128,3
download-xml-invoice-lambda-dev,2024-01-25 16:57:37 UTC,No disponible,nodejs18.x,,256,120
COB-postgiro-cob-monitoring-sqs-monitor,2025-02-12 21:48:49 UTC,No disponible,nodejs20.x,,128,10
order-service-rc-mimetus-update-creditnote,2024-10-17 15:20:25 UTC,No disponible,nodejs20.x,,2048,30
contacts-dev-updateBusinessContact,2025-06-12 20:14:37 UTC,No disponible,nodejs20.x,,1024,30
debt-prod-send-daily-process-messages-trigger-fn,2025-07-29 01:44:10 UTC,2025-07-29 03:00:00 UTC,nodejs20.x,,128,180
xepelin-authorizer-prod-appsync-authorizer,2023-06-06 16:07:06 UTC,2025-07-29 21:00:00 UTC,nodejs18.x,,1024,5
order-service-rc-auto-update-issued-date-MX,2024-10-17 15:20:25 UTC,2025-07-29 03:00:00 UTC,nodejs20.x,,2048,900
prd-cob-partial-pay-event-update-debt,2025-06-12 20:22:31 UTC,No disponible,nodejs20.x,,128,3
prd-cob-assignment-get-collector,2025-02-13 19:25:16 UTC,2025-07-29 17:00:00 UTC,nodejs20.x,,128,3
order-service-prod-origination-migration-consumer-CL,2025-06-03 21:42:44 UTC,2025-07-29 19:00:00 UTC,nodejs20.x,,2048,30
COB-postgiro-cob-debt-get-summary-products,2025-03-18 15:08:26 UTC,No disponible,nodejs20.x,,128,3
debt-prod-update-expired-trigger-fn,2025-07-29 01:43:45 UTC,2025-07-29 05:00:00 UTC,nodejs20.x,,128,180
order-service-prod-dlq-alarm-lambda,2025-06-03 21:42:44 UTC,2025-07-29 21:00:00 UTC,nodejs16.x,,128,3
COB-test-sns-cob-assignment-prepare-assignments,2025-03-19 13:26:02 UTC,No disponible,nodejs20.x,,256,240
debt-prod-payer-contribution-sync-trigger-fn,2025-07-29 01:43:28 UTC,2025-07-29 11:00:00 UTC,nodejs20.x,,128,900
order-service-rc-order-invoices-migration-MX,2024-10-17 15:20:25 UTC,No disponible,nodejs20.x,,2048,30
xepelin-kams-lambda-stg,2024-08-01 20:35:57 UTC,2025-05-23 19:00:00 UTC,nodejs18.x,,512,120
sg-sat-paids-events-replay-test-function,2024-08-27 01:17:37 UTC,No disponible,nodejs18.x,,1024,900
AutomationStateOrderStack-notifyAutomationDelayHan-ICnh7KukkMO2,2025-05-05 13:05:22 UTC,No disponible,nodejs20.x,,128,30
sg-business-events-replay-dev-function,2025-06-12 20:13:03 UTC,No disponible,nodejs20.x,,1024,900
contacts-dev-createContacts,2025-06-12 20:14:38 UTC,No disponible,nodejs20.x,,1024,30
order-invoice-enricher-mx-prod,2024-11-12 22:09:27 UTC,2025-07-29 21:00:00 UTC,nodejs20.x,,1024,60
