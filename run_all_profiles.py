#!/usr/bin/env python3
"""
Script para ejecutar get_lambda_info.py en todos los perfiles AWS configurados
"""

import subprocess
import sys
import os
from datetime import datetime

def get_aws_profiles():
    """Obtiene la lista de perfiles AWS configurados"""
    try:
        result = subprocess.run(['aws', 'configure', 'list-profiles'], 
                              capture_output=True, text=True, check=True)
        profiles = [profile.strip() for profile in result.stdout.strip().split('\n') if profile.strip()]
        return profiles
    except subprocess.CalledProcessError as e:
        print(f"Error obteniendo perfiles AWS: {e}", file=sys.stderr)
        return []

def check_profile_authentication(profile):
    """Verifica si un perfil está autenticado"""
    try:
        result = subprocess.run(['aws', 'sts', 'get-caller-identity', '--profile', profile], 
                              capture_output=True, text=True, check=True, timeout=30)
        return True, result.stdout
    except (subprocess.CalledProcessError, subprocess.TimeoutExpired) as e:
        return False, str(e)

def run_lambda_info_for_profile(profile):
    """Ejecuta get_lambda_info.py para un perfil específico"""
    try:
        print(f"\n{'='*60}")
        print(f"Procesando perfil: {profile}")
        print(f"{'='*60}")
        
        # Verificar autenticación
        is_authenticated, auth_info = check_profile_authentication(profile)
        if not is_authenticated:
            print(f"❌ Perfil {profile} no está autenticado o no es accesible")
            print(f"   Error: {auth_info}")
            return False
        
        print(f"✅ Perfil {profile} autenticado correctamente")
        
        # Ejecutar el script
        result = subprocess.run([
            'python3', 'get_lambda_info.py', 
            '--profile', profile, 
            '--format', 'csv'
        ], capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print(f"✅ Completado exitosamente para perfil {profile}")
            # Mostrar las últimas líneas del output (información de guardado)
            output_lines = result.stdout.strip().split('\n')
            if output_lines:
                print(f"   {output_lines[-1]}")
            return True
        else:
            print(f"❌ Error procesando perfil {profile}")
            print(f"   Error: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"❌ Timeout procesando perfil {profile} (más de 5 minutos)")
        return False
    except Exception as e:
        print(f"❌ Error inesperado procesando perfil {profile}: {e}")
        return False

def main():
    """Función principal"""
    print("🚀 Iniciando procesamiento de todos los perfiles AWS")
    print(f"📅 Fecha y hora: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Obtener perfiles
    profiles = get_aws_profiles()
    if not profiles:
        print("❌ No se encontraron perfiles AWS configurados")
        sys.exit(1)
    
    print(f"📋 Perfiles encontrados: {len(profiles)}")
    for i, profile in enumerate(profiles, 1):
        print(f"   {i}. {profile}")
    
    # Procesar cada perfil
    successful_profiles = []
    failed_profiles = []
    
    for profile in profiles:
        success = run_lambda_info_for_profile(profile)
        if success:
            successful_profiles.append(profile)
        else:
            failed_profiles.append(profile)
    
    # Resumen final
    print(f"\n{'='*60}")
    print("📊 RESUMEN FINAL")
    print(f"{'='*60}")
    print(f"✅ Perfiles procesados exitosamente: {len(successful_profiles)}")
    for profile in successful_profiles:
        print(f"   • {profile}")
    
    if failed_profiles:
        print(f"\n❌ Perfiles con errores: {len(failed_profiles)}")
        for profile in failed_profiles:
            print(f"   • {profile}")
    
    print(f"\n📁 Archivos CSV generados:")
    try:
        result = subprocess.run(['ls', '-la', 'lambda_info_*.csv'], 
                              capture_output=True, text=True, shell=True)
        if result.returncode == 0:
            print(result.stdout)
        else:
            print("   No se encontraron archivos CSV")
    except Exception as e:
        print(f"   Error listando archivos: {e}")
    
    print(f"\n🎉 Procesamiento completado!")
    print(f"📅 Finalizado: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
