name,created_date,last_invocation,runtime,description,memory_size,timeout
prd-fiscal-debt-cl-bulk-insert-f29,2025-07-09 21:46:32 UTC,2025-07-29 03:55:00 UTC,nodejs20.x,Example description,128,900
prd-invoice-wrapper-mx-enqueueDefaultPeriod,2025-07-25 19:49:33 UTC,2025-07-29 19:55:00 UTC,nodejs20.x,Example description,1024,30
prd-invoice-wrapper-cl-dailyAlert,2025-07-29 20:07:34 UTC,2025-07-28 22:55:00 UTC,nodejs20.x,Example description,128,900
prd-credentials-manager-mx-loginSatIsr,2025-06-30 22:01:41 UTC,2025-07-08 19:55:00 UTC,nodejs20.x,Example description,2048,600
prd-fiel-certificate-mx-consult-document,2025-03-31 12:26:12 UTC,No disponible,nodejs20.x,Example description,256,30
prd-veritrade-mx-process-extraction,2025-07-29 20:01:56 UTC,2025-07-29 19:55:00 UTC,nodejs20.x,Example description,1024,900
prd-fiscal-debt-cl-entry,2025-07-09 21:46:31 UTC,2025-07-17 13:55:00 UTC,nodejs20.x,Example description,128,30
prd-fiscal-debt-cl-mapper-f29,2025-07-09 21:46:31 UTC,2025-07-29 03:55:00 UTC,nodejs20.x,Example description,128,900
prd-invoice-wrapper-mx-jobProgress,2025-07-25 19:49:37 UTC,2025-07-15 16:55:00 UTC,nodejs20.x,Example description,512,30
prd-big-brother-mx-get-status,2025-06-11 20:59:12 UTC,No disponible,nodejs20.x,Example description,512,30
prd-invoice-wrapper-mx-enqueueUuid,2025-07-25 19:49:26 UTC,2025-07-29 18:55:00 UTC,nodejs20.x,Example description,512,30
prd-veritrade-mx-fan-in,2025-07-29 20:01:54 UTC,No disponible,nodejs20.x,Example description,2048,900
prd-invoice-wrapper-cl-enqueueDefaultPeriod,2025-07-25 19:40:13 UTC,2025-07-29 20:55:00 UTC,nodejs20.x,Example description,1024,30
prd-shared-infra-cl-sqs-purge,2025-03-14 15:58:03 UTC,No disponible,nodejs20.x,Example description,256,900
prd-siger-mx-getBusinessRelations,2024-11-12 20:37:25 UTC,No disponible,nodejs20.x,Example description,512,30
prd-siger-mx-getStakeholderRelations,2024-11-12 20:37:23 UTC,No disponible,nodejs20.x,Example description,512,30
prd-sat69-scrapper-mx-queueRequestManager,2025-03-21 14:21:34 UTC,2025-07-29 19:55:00 UTC,nodejs20.x,Example description,128,900
prd-pdf-parser-mx-pdfparserPypdf2,2024-11-07 12:31:03 UTC,2025-07-29 19:55:00 UTC,python3.8,Example description,512,30
prd-fiscal-situation-mx-scraper,2025-07-22 22:51:32 UTC,2025-07-26 06:55:00 UTC,nodejs20.x,Lambda that scrapes the fiscal situation of a business,2048,300
prd-credentials-manager-mx-retry-login,2025-06-30 22:01:40 UTC,2025-07-29 19:55:00 UTC,nodejs20.x,Example description,128,600
prd-credentials-manager-cl-retry-login,2025-06-30 22:01:45 UTC,2025-07-29 19:55:00 UTC,nodejs20.x,Example description,128,600
prd-credentials-manager-extension-audit-lambda-cl-getCredentials,2024-11-12 18:18:55 UTC,2025-07-29 20:55:00 UTC,nodejs20.x,Example description,128,120
prd-fiscal-debt-cl-extractor-f45,2025-07-09 21:46:31 UTC,2025-07-28 21:55:00 UTC,nodejs20.x,Example description,128,900
prd-siger-mx-scrapperStakeholder,2024-11-12 20:37:25 UTC,2025-07-29 20:55:00 UTC,nodejs20.x,Example description,512,300
prd-tgr-cl-mapper-debt,2025-06-25 19:16:01 UTC,2025-07-19 18:55:00 UTC,nodejs20.x,Example description,128,30
prd-rug-sign-mx-entry,2025-07-02 20:15:13 UTC,2025-07-29 19:55:00 UTC,nodejs20.x,Example description,512,720
prd-fiscal-situation-mx-enqueue,2025-07-22 22:51:25 UTC,2025-07-29 12:55:00 UTC,nodejs20.x,Lambda that receive requests to enqueue a business,512,30
prd-invoice-wrapper-cl-userCreatedEvents,2025-07-29 20:07:35 UTC,2025-07-29 20:55:00 UTC,nodejs20.x,Example description,512,30
prd-invoice-wrapper-mx-checkHistory,2025-07-25 19:49:18 UTC,2025-07-29 19:55:00 UTC,nodejs20.x,Example description,256,120
prd-veritrade-mx-enqueue-extraction,2025-07-29 20:01:55 UTC,2025-07-29 19:55:00 UTC,nodejs20.x,Example description,128,30
prd-invoice-stakeholder-updater-CL-update,2025-04-09 21:02:32 UTC,2025-07-14 15:55:00 UTC,nodejs20.x,Example description,128,900
prd-shared-infra-cl-sqs-redrive,2025-03-12 15:23:16 UTC,No disponible,nodejs20.x,Example description,256,900
prd-tgr-cl-purge-queue,2025-06-25 19:15:58 UTC,2025-07-29 10:55:00 UTC,nodejs20.x,Example description,128,300
prd-siger-mx-enqueueBusiness,2024-11-12 20:37:24 UTC,No disponible,nodejs20.x,Example description,512,30
prd-big-brother-mx-last-status,2025-06-11 20:59:15 UTC,No disponible,nodejs20.x,Example description,512,30
prd-mimetus-mx-response,2025-07-10 14:17:13 UTC,No disponible,nodejs20.x,Example description,256,30
prd-mimetus-v2-mx-invoices-json-retriever,2025-06-26 15:07:48 UTC,No disponible,nodejs20.x,Example description,128,30
prd-veritrade-mx-process-products-extraction,2025-07-29 20:01:54 UTC,No disponible,nodejs20.x,Example description,1024,900
prd-sat69-scrapper-mx-retrievePdf,2025-03-21 14:21:34 UTC,2025-07-29 19:55:00 UTC,nodejs20.x,Example description,128,3
prd-mimetus-v2-mx-redrive-dlqs,2025-06-25 20:11:43 UTC,2025-07-29 19:55:00 UTC,nodejs20.x,Example description,128,30
prd-invoice-wrapper-mx-cronEnqueue,2025-07-29 20:07:32 UTC,2025-07-17 04:55:00 UTC,nodejs20.x,Example description,1024,900
prd-rug-mx-health-check,2025-05-28 20:13:32 UTC,2025-07-29 19:55:00 UTC,nodejs20.x,Example description,512,720
prd-fiel-certificate-mx-entry,2025-03-31 12:26:13 UTC,2025-07-29 19:55:00 UTC,nodejs20.x,Example description,256,30
prd-invoice-wrapper-mx-storeEvents,2025-07-25 19:49:40 UTC,2025-07-29 19:55:00 UTC,nodejs20.x,Example description,128,30
prd-invoice-wrapper-mx-userCreatedEvents,2025-07-29 20:07:33 UTC,2025-07-29 19:55:00 UTC,nodejs20.x,Example description,512,30
prd-siger-mx-scrapperBusiness,2024-11-12 20:37:23 UTC,No disponible,nodejs20.x,Example description,512,300
prd-invoice-wrapper-cl-storeEvents,2025-07-25 19:40:00 UTC,2025-07-29 20:55:00 UTC,nodejs20.x,Example description,128,30
prd-invoice-wrapper-cl-enqueueCustomPeriod,2025-07-25 19:40:12 UTC,2025-07-29 13:55:00 UTC,nodejs20.x,Example description,1024,30
prd-invoice-stakeholder-updater-CL-enqueueToUpdate,2025-04-09 21:02:30 UTC,2025-07-29 19:55:00 UTC,nodejs20.x,Example description,128,900
aws-controltower-NotificationForwarder,2023-10-02 19:27:13 UTC,2025-07-29 17:55:00 UTC,python3.9,SNS message forwarding function for aggregating account notifications.,128,60
prd-credentials-manager-cl-checkPfxs,2025-06-30 22:01:46 UTC,2025-07-29 05:55:00 UTC,nodejs20.x,Example description,128,900
prd-invoice-wrapper-cl-cronEnqueue,2025-07-29 20:07:35 UTC,No disponible,nodejs20.x,Example description,1024,900
prd-credentials-manager-mx-loginSFS,2025-06-30 22:01:41 UTC,2025-07-29 19:55:00 UTC,nodejs20.x,Example description,2048,600
prd-invoice-wrapper-cl-userDeactivatedEvents,2025-07-29 20:07:42 UTC,2025-07-29 18:55:00 UTC,nodejs20.x,Example description,512,30
prd-invoice-wrapper-mx-setHistory,2025-07-29 20:07:31 UTC,2025-07-29 19:55:00 UTC,nodejs20.x,Example description,128,30
prd-veritrade-mx-mapper,2025-07-29 20:01:55 UTC,2025-07-29 19:55:00 UTC,nodejs20.x,Example description,2048,900
prd-fiscal-debt-cl-mapper-f45,2025-07-09 21:46:36 UTC,2025-07-28 21:55:00 UTC,nodejs20.x,Example description,128,900
prd-captcha-solver-mx-solveCaptcha,2025-05-26 20:14:34 UTC,2025-07-29 20:55:00 UTC,nodejs20.x,Example description,512,300
prd-invoice-wrapper-mx-userDeactivatedEvents,2025-07-29 20:07:37 UTC,2025-07-29 11:55:00 UTC,nodejs20.x,Example description,512,30
prd-big-brother-mx-event-cleaner,2025-06-11 20:59:09 UTC,2025-07-29 02:55:00 UTC,nodejs20.x,Example description,512,900
prd-veritrade-mx-bulk-insert,2025-07-29 20:01:55 UTC,2025-07-29 20:55:00 UTC,nodejs20.x,Example description,2048,900
prd-invoice-wrapper-mx-enqueueCustomPeriod,2025-07-25 19:49:33 UTC,2025-07-23 17:55:00 UTC,nodejs20.x,Example description,1024,30
prd-rug-mx-cron-health-check,2025-05-28 20:13:34 UTC,2025-07-29 19:55:00 UTC,nodejs20.x,Example description,512,300
prd-invoice-wrapper-cl-bulkInsert,2025-07-29 20:07:34 UTC,2025-07-29 20:55:00 UTC,nodejs20.x,Example description,1024,900
prd-credentials-manager-mx-loginSat69,2025-06-30 22:01:40 UTC,2025-07-29 19:55:00 UTC,nodejs20.x,Example description,2048,600
prd-mimetus-v2-mx-last-extraction,2025-06-25 20:11:44 UTC,2025-07-29 20:55:00 UTC,nodejs20.x,Example description,128,30
prd-invoice-wrapper-cl-setHistory,2025-07-29 20:07:41 UTC,2025-07-29 20:55:00 UTC,nodejs20.x,Example description,128,30
prd-captcha-solver-mx-checkBalance,2025-05-26 20:14:35 UTC,2025-07-29 17:55:00 UTC,nodejs20.x,Example description,512,30
prd-mimetus-v2-mx-single-invoice-downloader,2025-06-26 15:07:48 UTC,2025-07-29 15:55:00 UTC,nodejs20.x,Example description,128,30
prd-invoice-wrapper-mx-cronReprocessExtractions,2025-07-25 19:49:25 UTC,2025-07-29 19:55:00 UTC,nodejs20.x,Example description,128,900
prd-big-brother-cl-last-status,2025-06-19 15:20:38 UTC,No disponible,nodejs20.x,Example description,512,30
prd-mimetus-v2-mx-bulk-invoice-downloader,2025-06-26 15:07:48 UTC,2025-07-29 14:55:00 UTC,nodejs20.x,Example description,128,30
prd-tgr-cl-entry,2025-06-25 19:16:04 UTC,2025-07-29 20:55:00 UTC,nodejs20.x,Example description,128,30
prd-sat69-scrapper-mx-triggerEnqueue,2025-03-21 14:21:34 UTC,2025-07-29 19:55:00 UTC,nodejs20.x,Example description,128,900
prd-mimetus-mx-entry,2025-07-10 14:17:20 UTC,2025-07-29 19:55:00 UTC,nodejs20.x,Example description,256,30
prd-sat69-scrapper-mx-checkSat69,2025-07-22 18:12:51 UTC,2025-07-29 19:55:00 UTC,nodejs20.x,Lambda that scrapes the Sat69,1024,600
prd-fiscal-situation-mx-cron,2025-07-22 23:05:35 UTC,2025-07-29 06:55:00 UTC,nodejs20.x,Cron job to update fiscal situation of all clients that are not updated in the last month,512,300
prd-siger-mx-enqueueStakeholder,2024-11-12 20:37:25 UTC,2025-07-29 19:55:00 UTC,nodejs20.x,Example description,512,30
prd-invoice-wrapper-mx-reprocessExtractions,2025-07-25 19:49:19 UTC,2025-07-29 19:55:00 UTC,nodejs20.x,Example description,1024,60
prd-mimetus-mx-process-invoices,2025-07-10 14:17:28 UTC,2025-07-29 20:55:00 UTC,nodejs20.x,Example description,1024,900
prd-fiscal-debt-cl-get-fiscal-debt,2025-07-09 21:46:29 UTC,2025-07-17 12:55:00 UTC,nodejs20.x,Example description,128,30
prd-invoice-wrapper-mx-bulkInsert,2025-07-29 20:07:40 UTC,2025-07-29 20:55:00 UTC,nodejs20.x,Example description,1024,900
prd-big-brother-cl-store-events,2025-06-19 15:20:41 UTC,2025-07-29 20:55:00 UTC,nodejs20.x,Example description,512,900
prd-mimetus-mx-fan-in,2025-07-10 14:17:33 UTC,2025-07-29 20:55:00 UTC,nodejs20.x,Example description,1024,600
prd-fiscal-debt-cl-fan-in,2025-07-09 21:46:38 UTC,2025-07-29 03:55:00 UTC,nodejs20.x,Example description,128,900
prd-cession-pdf-cl-enqueue-download,2025-05-07 19:05:27 UTC,2025-07-24 13:55:00 UTC,nodejs20.x,Example description,512,30
prd-invoice-wrapper-mx-dailyAlert,2025-07-29 20:07:32 UTC,2025-07-28 22:55:00 UTC,nodejs20.x,Example description,128,900
prd-shared-infra-mx-sqs-redrive,2025-03-12 15:08:50 UTC,No disponible,nodejs20.x,Example description,256,900
prd-pdf-parser-mx-pdfparser,2024-11-07 12:31:03 UTC,No disponible,python3.8,Example description,512,30
prd-invoice-wrapper-cl-checkHistory,2025-07-25 19:40:06 UTC,2025-07-29 20:55:00 UTC,nodejs20.x,Example description,256,120
prd-big-brother-mx-store-events,2025-06-19 15:21:09 UTC,2025-07-29 20:55:00 UTC,nodejs20.x,Example description,512,900
prd-cession-pdf-cl-download-sii-pdf,2025-05-07 19:05:27 UTC,2025-07-24 13:55:00 UTC,nodejs20.x,Example description,512,300
prd-fiel-certificate-mx-extractor,2025-03-31 12:26:22 UTC,2025-07-29 20:55:00 UTC,nodejs20.x,Example description,2048,900
prd-fiscal-situation-mx-enroll,2025-07-22 23:05:35 UTC,2025-07-29 20:55:00 UTC,nodejs20.x,Lambda that enqueue a job to obtain fiscal situtation for an enrolled business,256,30
prd-fiscal-debt-cl-bulk-insert-f45,2025-07-09 21:46:30 UTC,2025-07-28 21:55:00 UTC,nodejs20.x,Example description,128,900
prd-fiscal-debt-cl-enqueue,2025-07-09 21:46:35 UTC,2025-07-28 21:55:00 UTC,nodejs20.x,Example description,128,900
prd-mimetus-mx-extraction-progress,2025-07-10 14:17:24 UTC,2025-07-15 16:55:00 UTC,nodejs20.x,Example description,256,30
prd-fiscal-debt-cl-fan-out,2025-07-09 21:46:37 UTC,2025-07-28 21:55:00 UTC,nodejs20.x,Example description,128,900
prd-captcha-solver-mx-solveRecaptchaV3,2025-05-26 20:14:29 UTC,No disponible,nodejs20.x,Example description,512,30
prd-tgr-cl-mapper-court-case,2025-06-25 19:16:09 UTC,2025-07-19 17:55:00 UTC,nodejs20.x,Example description,128,30
prd-rug-mx-enqueue-identifiers,2025-05-28 20:13:33 UTC,No disponible,nodejs20.x,Example description,512,720
prd-rug-mx-cron-enqueue,2025-05-28 20:13:37 UTC,2025-07-29 11:55:00 UTC,nodejs20.x,Example description,512,300
prd-fiscal-debt-cl-download-f29,2025-07-09 21:46:30 UTC,2025-07-10 15:55:00 UTC,nodejs20.x,Example description,128,30
prd-shared-infra-mx-sqs-purge,2025-03-14 15:58:10 UTC,No disponible,nodejs20.x,Example description,256,900
prd-big-brother-cl-event-cleaner,2025-06-19 15:20:36 UTC,2025-07-29 02:55:00 UTC,nodejs20.x,Example description,512,900
prd-big-brother-cl-get-status,2025-06-19 15:20:41 UTC,No disponible,nodejs20.x,Example description,512,30
prd-invoice-wrapper-mx-cronEnqueueLDC,2025-07-29 20:07:40 UTC,2025-07-29 09:55:00 UTC,nodejs20.x,Example description,1024,900
prd-fiel-certificate-mx-consult,2025-03-31 12:26:19 UTC,2025-07-29 20:55:00 UTC,nodejs20.x,Example description,256,30
prd-invoice-stakeholder-updater-CL-requestToUpdate,2025-04-09 21:02:26 UTC,2025-07-14 15:55:00 UTC,nodejs20.x,Example description,128,900
prd-mimetus-v2-mx-check-notified,2025-06-25 20:11:42 UTC,No disponible,nodejs20.x,Example description,128,30
