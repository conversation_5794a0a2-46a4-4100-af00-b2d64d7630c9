name,created_date,last_invocation,runtime,description,memory_size,timeout
core-sofipo_notification-center_customer-block_mx_rc,2025-07-29 16:27:50 UTC,2025-07-29 13:00:00 UTC,nodejs20.x,Lambda function responsible for send customer block notification to slack.,256,240
core-sofipo_file-processor_report-validation_mx_rc,2025-07-23 17:06:20 UTC,2025-07-29 09:00:00 UTC,nodejs20.x,Lambda function responsible for validating daily Tritium reports and sending Slack notifications for missing reports.,512,300
core-sofipo_core-accounting_processor_mx_dev-mx,2025-07-22 17:53:54 UTC,2025-07-26 09:00:00 UTC,nodejs20.x,Lambda for processing Netsuite requests,128,60
core-sofipo_operational-reports_statements-access_mx_rc,2025-07-23 17:06:21 UTC,2025-06-25 19:00:00 UTC,nodejs20.x,Lambda function responsible for access data to generate account statements.,256,240
core-sofipo_core-banking_transaction-logger_mx_rc,2025-06-19 15:37:47 UTC,No disponible,nodejs20.x,Example description,128,60
core-sofipo_notification-center_layout-event_mx_dev,2025-07-29 14:13:44 UTC,2025-06-27 21:00:00 UTC,nodejs20.x,Lambda function responsible for processing messages from the queue.,128,180
core-sofipo_operational-reports_accounting-generator_mx_dev,2025-07-23 17:02:07 UTC,2025-07-29 09:00:00 UTC,nodejs20.x,Lambda function responsible for scheduled accounting generation.,256,240
core-sofipo_operational-reports_tritium-data-loader_mx_rc,2025-07-23 17:06:20 UTC,2025-07-29 19:00:00 UTC,nodejs20.x,Lambda function responsible for save tritium data in the database,256,290
core-sofipo_operational-reports_accounting-generator_mx_rc,2025-07-23 17:06:21 UTC,2025-07-29 11:00:00 UTC,nodejs20.x,Lambda function responsible for scheduled accounting generation.,256,240
core-sofipo_file-processor_operational-reports_mx_dev,2025-07-23 16:47:34 UTC,2025-06-13 15:00:00 UTC,nodejs20.x,File processor Lambda,512,180
core-sofipo_tritium-webhook_tritium-customer-notification_mx_rc,2025-07-23 17:06:17 UTC,2025-07-29 17:00:00 UTC,nodejs20.x,Lambda function responsible for send customer block notification to slack.,256,240
core-sofipo_notification-center_layout-event_mx_rc,2025-07-29 16:27:48 UTC,2025-05-12 11:00:00 UTC,nodejs20.x,Lambda function responsible for processing messages from the queue.,128,180
core-sofipo_core-accounting_notifier_mx_dev-mx,2025-07-22 17:53:56 UTC,2025-06-27 21:00:00 UTC,nodejs20.x,Lambda function that processes accounting errors and sends alerts to Slack.,128,30
core-sofipo_tritium-webhook_tritium-customer-notification_mx_dev,2025-07-24 16:34:00 UTC,2025-06-19 15:00:00 UTC,nodejs20.x,Lambda function responsible for send customer block notification to slack.,256,240
core-sofipo_core-accounting_receptor_mx_dev-mx,2025-07-22 17:53:50 UTC,2025-07-26 09:00:00 UTC,nodejs20.x,Lambda for receiving accounting requests,128,60
core-sofipo_operational-reports_statements-account-data_mx_dev,2025-07-23 17:02:08 UTC,2025-07-01 11:00:00 UTC,nodejs20.x,Lambda function responsible for generating data for account statements.,256,240
core-sofipo_core-banking_transaction-logger_mx_dev,2025-07-23 15:26:39 UTC,No disponible,nodejs20.x,Example description,128,60
core-sofipo_notification-center_banking-account-notify_mx_rc,2025-07-29 16:27:55 UTC,2025-07-29 17:00:00 UTC,nodejs20.x,Lambda function responsible for sending account notifications to core banking.,256,240
core-sofipo_tritium-webhook_test-login-lambda_mx_dev,2025-07-24 16:50:56 UTC,2025-07-21 19:00:00 UTC,nodejs20.x,Test lambda function that calls the Tritium login endpoint through AWS Private Link for testing connectivity.,256,30
core-sofipo_notification-center_banking-account-notify_mx_dev,2025-07-29 14:13:50 UTC,2025-07-29 09:00:00 UTC,nodejs20.x,Lambda function responsible for sending account notifications to core banking.,256,240
core-sofipo_operational-reports_statements-access_mx_dev,2025-07-23 17:02:07 UTC,2025-06-19 17:00:00 UTC,nodejs20.x,Lambda function responsible for access data to generate account statements.,256,240
core-sofipo_core-accounting_slackNotification_mx_prd,2024-11-28 15:58:20 UTC,No disponible,nodejs20.x,Example description,128,60
core-sofipo_notification-center_customer-block_mx_dev,2025-07-29 14:13:44 UTC,No disponible,nodejs20.x,Lambda function responsible for send customer block notification to slack.,256,240
core-sofipo_file-processor_report-validation_mx_dev,2025-07-23 16:47:34 UTC,2025-07-29 09:00:00 UTC,nodejs20.x,Lambda function responsible for validating daily Tritium reports and sending Slack notifications for missing reports.,512,300
core-sofipo_operational-reports_tritium-data-loader_mx_dev,2025-07-23 17:02:08 UTC,2025-06-27 13:00:00 UTC,nodejs20.x,Lambda function responsible for save tritium data in the database,256,290
core-sofipo_tritium-webhook_test-login-lambda_mx_rc,2025-07-23 17:06:16 UTC,No disponible,nodejs20.x,Test lambda function that calls the Tritium login endpoint through AWS Private Link for testing connectivity.,256,30
core-sofipo_file-processor_operational-reports_mx_rc,2025-07-23 17:06:25 UTC,2025-07-29 19:00:00 UTC,nodejs20.x,File processor Lambda,512,180
aws-controltower-NotificationForwarder,2024-07-10 19:11:29 UTC,2025-07-23 17:00:00 UTC,python3.9,SNS message forwarding function for aggregating account notifications.,128,60
core-sofipo_notification-center_slack-notification_mx_dev,2025-07-29 14:13:34 UTC,2025-07-29 09:00:00 UTC,nodejs20.x,Lambda function responsible for exposing rest service to send notifications to slack.,256,240
core-sofipo_core-accounting_layoutCobranzaOriginacion_mx_dev,2024-12-11 19:03:22 UTC,No disponible,nodejs20.x,Example description,128,60
core-sofipo_notification-center_slack-notification_mx_rc,2025-07-29 16:27:38 UTC,2025-07-29 13:00:00 UTC,nodejs20.x,Lambda function responsible for exposing rest service to send notifications to slack.,256,240
core-sofipo_core-accounting_layout-cobranza-intereses_mx_rc,2024-12-17 15:38:22 UTC,No disponible,nodejs20.x,Example description,128,180
core-sofipo_operational-reports_statements-account-data_mx_rc,2025-07-23 17:06:21 UTC,2025-07-01 11:00:00 UTC,nodejs20.x,Lambda function responsible for generating data for account statements.,256,240
