name,created_date,last_invocation,runtime,description,memory_size,timeout
xepelin-sls-consult-dicom-staging-enqueue,2022-07-07 00:14:29 UTC,No disponible,nodejs14.x,,2048,30
xepelin-sls-funds-portal-staging-abstrahereCessionV2,2025-07-25 20:51:47 UTC,2025-07-29 11:35:00 UTC,nodejs20.x,,2048,300
xepelin-sls-quotations-staging-simulateQuotation,2025-07-28 20:04:22 UTC,2025-07-16 18:35:00 UTC,nodejs18.x,,1024,10
xepelin-sls-receipts-staging-supplierReceiptPayroll,2022-09-08 15:34:19 UTC,No disponible,nodejs14.x,,2048,60
xepelin-sls-funds-portal-staging-getReceiverByIdentifier,2025-07-25 20:51:48 UTC,No disponible,nodejs20.x,,1024,30
xepelin-sls-reporteria-fondos-staging-register,2021-10-22 18:00:36 UTC,No disponible,nodejs12.x,,1024,6
57f5qyn-mwny9en,2022-01-03 12:21:50 UTC,No disponible,nodejs14.x,Image Lambda@Edge for Next CloudFront distribution,512,30
xepelin-sls-receipts-stg-cl-suplierReceipt,2023-04-14 14:01:41 UTC,No disponible,nodejs14.x,,1024,30
CF_backoffice_devel,2022-01-07 22:51:14 UTC,No disponible,nodejs14.x,,128,3
xepelin-sls-syncfy-update,2022-08-31 13:52:21 UTC,2025-07-29 17:35:00 UTC,nodejs16.x,Servicio que consulta y almacena la información bancaria desde syncfy,128,180
xepelin-sls-generate-pdf-prod-getPaymentSimulationPdf,2023-01-06 20:16:55 UTC,No disponible,nodejs16.x,,2048,30
xepelin-sls-rejected-order-notifier-staging-sendNotification,2025-02-25 15:58:19 UTC,2025-07-23 21:35:00 UTC,nodejs18.x,,1024,60
xepelin-sls-n8n-credentials-map-staging-getCredentialId,2025-07-29 15:44:01 UTC,2025-07-29 16:35:00 UTC,nodejs20.x,,1024,6
xepelin-sls-parse-sns-to-amplitude-prod-parseSnsToAmplitude,2025-02-25 16:00:45 UTC,2025-07-29 18:35:00 UTC,nodejs18.x,,1024,30
7nqluv4-e7ctoi,2022-01-04 02:22:45 UTC,No disponible,nodejs14.x,Default Lambda@Edge for Next CloudFront distribution,512,30
xepelin-sls-funds-portal-staging-downloadInvoicePdf,2025-07-25 20:51:43 UTC,No disponible,nodejs20.x,,1024,30
xepelin-sls-bgc-workflow-staging-automatizationsQueue,2025-06-05 23:18:57 UTC,2025-07-29 18:35:00 UTC,nodejs18.x,,1024,90
xepelin-sls-funds-portal-staging-processInvoiceAttachmentDeleted,2025-07-25 20:51:48 UTC,No disponible,nodejs20.x,,2048,300
xepelin-sls-invoices-tags-prod-addInvoiceTags,2025-07-22 15:07:21 UTC,2025-07-29 18:35:00 UTC,nodejs18.x,,1024,10
wallet-cxc-sls-prod-fetchDocuments,2024-06-06 20:30:52 UTC,No disponible,nodejs18.x,,1024,900
xaas-01hm-hd99-qrsn-sync,2024-01-19 18:59:07 UTC,No disponible,python3.12,,128,3
xepelin-sls-open-wallet-from-hubspot-prod-receive-events,2024-12-19 13:15:37 UTC,2025-07-28 21:35:00 UTC,nodejs18.x,,1024,90
xepelin-sls-funds-portal-staging-getPayrollInvoices,2025-07-25 20:51:43 UTC,2025-07-29 11:35:00 UTC,nodejs20.x,,1024,30
xepelin-sls-parse-sns-to-amplitude-dev-parseSnsToAmplitude,2025-02-24 21:33:08 UTC,2025-07-23 20:35:00 UTC,nodejs18.x,,1024,30
xepelin-sls-receipts-stg-mx-suplierReceipt,2023-04-14 14:01:05 UTC,No disponible,nodejs14.x,,1024,30
sls-third-party-xepelin-global-staging-globalEvents,2023-02-16 15:54:35 UTC,2025-07-29 18:35:00 UTC,nodejs16.x,,1024,6
xepelin-sls-accounts-staging-getProfileId,2024-10-28 22:36:54 UTC,No disponible,nodejs20.x,,1024,6
xepelin-sls-risk-module-prod-app,2023-02-20 21:21:56 UTC,No disponible,nodejs14.x,,128,30
datacrowd_pipeline-dynamo-bigquery_us_stg,2023-08-22 18:02:27 UTC,No disponible,N/A,,1048,3
cyc-bulk-csv-br-cl-sls-production-bulkCSVBankReconciliation,2024-05-02 14:10:17 UTC,No disponible,nodejs20.x,,1024,900
lambda_labeling_nodes_autoscale_stg,2025-02-26 12:14:10 UTC,No disponible,python3.8,,128,3
xepelin-sls-funds-portal-staging-getPayrolls,2025-07-25 20:51:47 UTC,2025-07-29 11:35:00 UTC,nodejs20.x,,1024,30
xepelin-sls-generate-one-click-deeplink-dev-getOneClickDeeplink,2024-03-08 18:10:38 UTC,No disponible,nodejs18.x,,1024,6
xepelin-sls-funds-portal-staging-createPayroll,2025-07-25 20:51:47 UTC,No disponible,nodejs20.x,,1024,30
humitas-test,2022-06-09 01:01:56 UTC,No disponible,N/A,,2048,360
xepelin-sls-n8n-credentials-map-staging-addCredential,2025-07-29 15:44:01 UTC,2025-07-29 16:35:00 UTC,nodejs20.x,,1024,6
xepelin-sls-funds-staging-invoices,2021-11-09 22:19:50 UTC,No disponible,nodejs12.x,,1024,6
xepelin-sls-receipts-staging-fileStructureS3,2022-09-08 15:34:18 UTC,No disponible,nodejs14.x,,5048,120
wallet-cxc-sls-staging-fetchDocuments,2024-04-18 17:02:11 UTC,No disponible,nodejs18.x,,1024,900
sls-payment-notification-bpc-cl-staging-sendToPuertoX,2022-04-27 20:29:44 UTC,No disponible,nodejs14.x,,1024,6
xepelin-sls-accounts-cl-staging-getBalanceByIdentifier,2024-07-08 18:37:09 UTC,2025-07-29 15:35:00 UTC,nodejs20.x,,1024,6
xepelin-sls-quotations-prod-getQuotations,2025-07-29 18:53:03 UTC,2025-07-29 18:35:00 UTC,nodejs18.x,,1024,10
sls-payment-notification-bpc-cl-staging-documentHistoryFromPX,2022-04-27 20:30:27 UTC,No disponible,nodejs14.x,,1024,6
xepelin-sls-accounts-cl-staging-createActivity,2024-07-08 18:37:09 UTC,No disponible,nodejs20.x,,1024,6
xepelin-sls-funds-portal-staging-payrollSimulateV2,2025-07-25 20:51:47 UTC,No disponible,nodejs20.x,,1024,30
xepelin-sls-syncfy,2022-08-16 15:50:19 UTC,No disponible,nodejs16.x,Servicio que retorna la informacion bancaria proveniente de syncfy,128,180
xepelin-sls-bnpl-dev-createBnplOrder,2025-07-22 15:37:58 UTC,No disponible,nodejs18.x,,1024,60
xepelin-sls-funds-getter-prod-funds,2022-11-03 18:20:34 UTC,No disponible,nodejs16.x,,1024,25
xepelin-sls-bgc-workflow-prod-customAuthorizer,2025-06-06 01:42:21 UTC,2025-07-29 18:35:00 UTC,nodejs18.x,,1024,6
xepelin-sns-event-tracker-staging-trackSNSEventInAmplitude,2025-03-05 13:55:17 UTC,2025-07-29 18:35:00 UTC,nodejs18.x,,1024,30
xepelin-sls-n8n-credentials-map-dev-getCredentialId,2025-07-29 15:21:41 UTC,2025-07-29 15:35:00 UTC,nodejs20.x,,1024,6
business-segment-prod-segmentHttp,2023-03-31 16:27:53 UTC,No disponible,nodejs16.x,,1024,60
xepelin-sls-reporteria-fondos-staging-status,2021-10-22 18:00:36 UTC,No disponible,nodejs12.x,,1024,6
xepelin-sls-consult-dicom-staging-consult,2022-07-07 00:14:28 UTC,No disponible,nodejs14.x,,2048,30
apicorps-add-confirming-invoices-post,2025-01-09 20:53:40 UTC,2025-07-29 13:35:00 UTC,nodejs16.x,apiCorps resolver for adding new confirmations to invoices,254,180
prod_asignacion_pago_CL,2025-01-24 14:49:05 UTC,2025-07-14 14:35:00 UTC,nodejs20.x,,1024,30
sls-bpc-movements-publisher-staging-processCustodyOnDemand,2022-09-06 16:06:59 UTC,No disponible,nodejs16.x,,1024,30
sls-payment-notification-bpc-cl-2805c27c84992586afceadbfac5a1faf,2022-04-27 20:29:45 UTC,No disponible,nodejs12.x,,1024,180
xepelin-sls-bulletin-uploader-prod-custom-resource-apigw-cw-role,2022-11-30 13:14:32 UTC,No disponible,nodejs14.x,,1024,180
xepelin-sls-accounts-staging-manageActivities,2024-10-28 22:36:54 UTC,No disponible,nodejs20.x,,1024,60
xepelin-sls-invoices-tags-staging-addInvoiceTags,2025-07-22 15:06:43 UTC,2025-07-29 18:35:00 UTC,nodejs18.x,,1024,10
prod_scheduled-billing-moratories-service,2023-09-12 22:28:00 UTC,No disponible,nodejs18.x,,1024,30
xepelin-sls-receipts-staging-supplierReceiptCL,2022-09-08 15:34:22 UTC,No disponible,nodejs14.x,,2048,30
xepelin-sls-buro-credito-staging-status,2024-09-17 12:40:19 UTC,No disponible,nodejs18.x,,1024,6
wallet-funds-sls-staging-getBorrowingData,2024-04-03 20:41:19 UTC,No disponible,nodejs20.x,,1024,900
xepelin-sls-funds-portal-staging-bulkDeletePayrollInvoice,2025-07-25 20:51:43 UTC,No disponible,nodejs20.x,,1024,30
xepelin-sls-quotations-proxy-staging-getQuotation,2025-07-22 15:06:58 UTC,2025-07-03 16:35:00 UTC,nodejs18.x,,1024,10
test_sls_lambda,2022-05-19 20:01:09 UTC,No disponible,nodejs14.x,,128,3
h5gxl74-qcfp9b,2022-01-03 11:55:38 UTC,No disponible,nodejs14.x,Default Lambda@Edge for Next CloudFront distribution,512,30
datadog-forwarder,2024-03-11 15:34:26 UTC,2025-07-29 18:35:00 UTC,python3.9,"Pushes logs, metrics and traces from AWS to Datadog.",1024,120
xepelin-sls-pdf-generator-prod-generatePdf,2025-01-14 17:46:03 UTC,2025-07-29 17:35:00 UTC,nodejs18.x,,1024,6
xepelin-sls-funds-getter-staging-funds,2022-10-21 16:07:16 UTC,No disponible,nodejs16.x,,1024,25
ec2_stop,2021-11-19 16:32:33 UTC,2025-07-28 23:35:00 UTC,python3.7,,128,600
xepelin-sls-n8n-credentials-map-prod-updateCredentialId,2025-07-29 18:53:37 UTC,No disponible,nodejs20.x,,1024,6
xepelin-sls-consult-dicom-staging-status,2022-07-07 00:14:29 UTC,No disponible,nodejs14.x,,1024,6
xepelin-sls-funds-staging-custom-resource-apigw-cw-role,2021-11-09 22:19:48 UTC,No disponible,nodejs12.x,,1024,180
xepelin-sls-funds-portal-staging-getPayrollPdfSummary,2025-07-25 20:51:49 UTC,No disponible,nodejs20.x,,1024,30
xepelin-sls-form-buro-staging-status,2025-06-04 17:53:22 UTC,No disponible,nodejs18.x,,1024,6
xepelin-sls-bgc-workflow-staging-triggerRunSubmissionQueue,2025-06-05 23:18:57 UTC,2025-07-29 17:35:00 UTC,nodejs18.x,,1024,90
xepelin-sls-check-credit-line-prod-checkCreditLine,2025-01-21 18:13:30 UTC,2025-07-29 18:35:00 UTC,nodejs18.x,,1024,15
xepelin-sls-form-buro-staging-verifyCode,2025-06-04 17:53:22 UTC,2025-07-23 21:35:00 UTC,nodejs18.x,,2048,30
xepelin-sls-funds-portal-staging-processInvoiceAssigned,2025-07-25 20:51:43 UTC,No disponible,nodejs20.x,,2048,300
xepelin-sls-consult-dicom-staging-custom-resource-apigw-cw-role,2022-07-07 00:14:29 UTC,No disponible,nodejs14.x,,1024,180
xepelin-sls-one-click-opportunities-dev-getOneClickOpps,2025-07-22 15:38:15 UTC,No disponible,nodejs18.x,,1024,60
xepelin-sls-generate-pdf-prod-getSimulationPdf,2023-01-06 20:16:55 UTC,No disponible,nodejs16.x,,2048,30
xepelin-sls-pdf-getter-staging-getPdf,2024-12-20 14:11:34 UTC,No disponible,nodejs18.x,,1024,6
wallet-xaas-client-activator-sls-staging-clientActivatorSns,2024-04-09 14:18:41 UTC,No disponible,nodejs18.x,,1024,6
notifications-sender-prod-sendSlack,2024-12-19 13:14:48 UTC,No disponible,nodejs18.x,,1024,6
xepelin-sls-transfers-staging-custom-resource-apigw-cw-role,2024-01-24 15:10:07 UTC,No disponible,nodejs16.x,,1024,180
xepelin-sls-reporteria-fondos-staging-login,2021-10-22 18:00:36 UTC,No disponible,nodejs12.x,,1024,6
xepelin-sls-invoices-tags-dev-addInvoiceTags,2025-07-22 15:38:53 UTC,2025-07-29 17:35:00 UTC,nodejs18.x,,1024,10
xepelin-sls-quotations-proxy-dev-updateQuotation,2025-07-22 15:39:27 UTC,No disponible,nodejs18.x,,1024,10
growth_redirects,2022-10-31 21:50:27 UTC,No disponible,nodejs14.x,,128,3
xepelin-sls-funds-staging-invoicesGroupedByPayer,2021-11-09 22:19:49 UTC,No disponible,nodejs12.x,,1024,6
migrations-stg-us-lambda,2025-01-21 20:03:13 UTC,2025-07-28 15:35:00 UTC,N/A,Lambda to perform migration on lower environments databases,2048,900
documents-change-status-cl-dev,2024-06-07 12:54:40 UTC,No disponible,nodejs20.x,Lambda to update the status of a document,128,60
1qq5x74-ni7h40q,2022-01-03 18:11:56 UTC,No disponible,nodejs14.x,Image Lambda@Edge for Next CloudFront distribution,512,30
stg_asignacion_pago_CL,2025-01-13 20:16:24 UTC,No disponible,nodejs20.x,,1024,30
xepelin-sls-global-invoices-risk-stag-processOrders-mx,2024-02-20 14:41:15 UTC,2025-07-29 18:35:00 UTC,nodejs20.x,,2048,120
stg_scheduled-billing-moratories-service,2023-08-22 16:27:52 UTC,No disponible,nodejs18.x,,1024,30
xepelin-sls-quotations-proxy-prod-generateQuotationPDF,2025-07-22 15:07:25 UTC,2025-07-29 18:35:00 UTC,nodejs18.x,,1024,15
xepelin-sls-funds-portal-staging-processFundUpdated,2025-07-25 20:51:43 UTC,No disponible,nodejs20.x,,2048,300
xepelin-sls-bnpl-staging-getBnplOrder,2025-02-25 15:59:02 UTC,No disponible,nodejs18.x,,1024,10
xepelin-sls-user-log-staging-get,2023-04-11 17:54:55 UTC,No disponible,nodejs16.x,,512,30
xepelin-sls-accounts-staging-createActivity,2024-10-28 22:36:54 UTC,No disponible,nodejs20.x,,1024,6
xepelin-sls-accounts-cl-staging-getAccountActivities,2024-07-08 18:37:09 UTC,No disponible,nodejs20.x,,1024,6
xepelin-sls-receipts-staging-supplierReceiptMX,2022-09-08 15:34:19 UTC,No disponible,nodejs14.x,,2048,30
57f5qyn-jt2ktv,2022-01-03 16:06:02 UTC,No disponible,nodejs14.x,Default Lambda@Edge for Next CloudFront distribution,512,30
xepelin-sls-form-buro-staging-sendSatSituation,2025-06-04 17:53:22 UTC,No disponible,nodejs18.x,,2048,30
docs-dw_xepelin_com-cloudfront_auth,2022-04-07 16:22:54 UTC,No disponible,nodejs12.x,OKTA authentication for docs-dw.xepelin.com,128,5
xepelin-sls-funds-portal-staging-getReceiversV2,2025-07-25 20:51:44 UTC,2025-07-29 16:35:00 UTC,nodejs20.x,,1024,30
balance-forecast-reminders-staging-sendBalanceForecastReminders,2024-12-17 13:01:47 UTC,No disponible,nodejs18.x,,1024,60
balance-forecast-reminders-prod-sendBalanceForecastReminders,2024-12-19 13:14:34 UTC,No disponible,nodejs18.x,,1024,60
xepelin-sls-transfers-cl-staging-custom-resource-apigw-cw-role,2024-01-24 15:17:42 UTC,No disponible,nodejs16.x,,1024,180
xepelin-sls-pdf-generator-staging-generatePdf,2025-01-10 22:04:09 UTC,No disponible,nodejs18.x,,1024,6
eh41d5d-fm5c5gl,2022-02-15 12:39:26 UTC,No disponible,nodejs14.x,Default Lambda@Edge for Next CloudFront distribution,512,30
cyc-event-processor-sls-production-processEvents,2024-03-07 22:00:42 UTC,No disponible,nodejs20.x,,1024,6
xepelin-sls-funds-portal-staging-processInvoiceAttachmentCreated,2025-07-25 20:51:49 UTC,2025-07-11 21:35:00 UTC,nodejs20.x,,2048,300
xepelin-sls-funds-portal-staging-payrollBulkUpdateStatus,2025-07-25 20:51:49 UTC,No disponible,nodejs20.x,,1024,30
xepelin-sls-client-notifications-staging-parseSnsToSendGrid,2025-06-09 12:26:24 UTC,2025-07-23 21:35:00 UTC,nodejs18.x,,1024,10
sls-bpc-movements-publisher-staging-processCustodyMovements,2022-09-06 16:07:00 UTC,No disponible,nodejs16.x,,2048,300
xepelin-sls-funds-getter-dev-funds,2022-11-02 01:22:07 UTC,No disponible,nodejs16.x,,1024,25
testLambda-da13394,2023-09-06 20:45:39 UTC,No disponible,nodejs18.x,,128,180
xepelin-sls-deeplink-generator-prod-generateDeeplink,2024-12-19 13:16:00 UTC,2025-07-29 18:35:00 UTC,nodejs18.x,,1024,6
xepelin-sls-accounts-cl-staging-updateAccountStatus,2024-07-08 18:37:09 UTC,No disponible,nodejs20.x,,1024,6
xepelin-sls-n8n-credentials-map-prod-getCredentialId,2025-07-29 18:53:37 UTC,2025-07-29 18:35:00 UTC,nodejs20.x,,1024,6
xepelin-sls-pdf-getter-prod-getPdf,2024-12-31 18:55:43 UTC,No disponible,nodejs18.x,,1024,6
xepelin-sls-accounts-staging-status,2024-10-28 22:36:54 UTC,No disponible,nodejs20.x,,1024,6
xepelin-sls-transfers-cl-staging-santander-scraper-sync,2024-01-24 15:17:22 UTC,No disponible,nodejs16.x,,2048,900
xepelin-sls-drop-funnel-notifier-prod-sendNotification,2025-02-25 16:01:20 UTC,2025-07-29 17:35:00 UTC,nodejs18.x,,1024,60
notifications-sender-dev-sendSlack,2025-07-22 15:38:45 UTC,No disponible,nodejs18.x,,1024,6
xepelin-sls-wallet-events-staging-status,2024-01-24 15:25:31 UTC,No disponible,nodejs16.x,,1024,6
xepelin-sls-one-click-opportunities-prod-getOneClickOpps,2025-02-25 16:01:46 UTC,No disponible,nodejs18.x,,1024,60
gmhfn34-fmf0jse,2022-01-04 00:37:13 UTC,No disponible,nodejs14.x,Default Lambda@Edge for Next CloudFront distribution,512,30
apicorps-dynamodb-stream-resolver,2023-01-10 03:23:45 UTC,2025-07-09 17:35:00 UTC,nodejs16.x,Handles dynamodb stream for API corps,254,180
xepelin-sls-bgc-workflow-staging-customAuthorizer,2025-06-05 23:18:57 UTC,2025-07-29 17:35:00 UTC,nodejs18.x,,1024,6
xepelin-sls-business-categorization-dev-getCategorization,2025-07-22 15:37:34 UTC,No disponible,nodejs18.x,,1024,15
xepelin-sls-receipts-staging-clientReceiptCL,2022-09-08 15:34:18 UTC,No disponible,nodejs14.x,,2048,30
xepelin-sls-transfers-cl-staging-santander-scraper,2024-01-24 15:16:58 UTC,No disponible,nodejs16.x,,2048,300
xepelin-sls-quotations-proxy-prod-convertQuotationToOrder,2025-07-22 15:07:25 UTC,2025-07-29 18:35:00 UTC,nodejs18.x,,1024,15
xepelin-sls-transfers-cl-production-santander-scraper,2024-01-24 15:13:30 UTC,No disponible,nodejs16.x,,2048,30
xepelin-sls-quotations-proxy-staging-getBusinessQuotations,2025-07-22 15:06:58 UTC,2025-07-29 18:35:00 UTC,nodejs18.x,,1024,10
xepelin-sls-risk-module-prod-portfolio,2023-02-20 21:21:56 UTC,2025-07-28 22:35:00 UTC,nodejs14.x,,128,900
serverless-flask-api-workshop-dev-api,2022-03-31 18:40:44 UTC,No disponible,python3.8,,1024,6
xepelin-sls-accounts-cl-staging-checkBalance,2024-07-08 18:37:09 UTC,No disponible,nodejs20.x,,1024,6
xepelin-sls-funds-portal-staging-btgSendPayroll,2025-07-25 20:51:43 UTC,No disponible,nodejs20.x,,1024,300
xepelin-portfolio-metrics,2024-08-27 02:51:54 UTC,No disponible,N/A,,4000,900
notifications-sender-dev-sendEmailByTemplateId,2025-07-22 15:38:44 UTC,2025-07-29 18:35:00 UTC,nodejs18.x,,1024,60
xepelin-sls-reporteria-fondos-staging-accessTokenAuthorizer,2021-10-22 18:00:35 UTC,No disponible,nodejs12.x,,1024,6
sls-global-invoices-risk-mx-staging-startInvoiceRiskOnView,2022-01-07 20:49:45 UTC,No disponible,nodejs14.x,,2048,300
cyc-bulk-csv-br-cl-sls-production-custom-resource-existing-s3,2024-05-02 14:10:16 UTC,No disponible,nodejs16.x,,1024,180
xepelin-sls-global-invoices-risk-stag-status,2024-02-20 14:41:16 UTC,No disponible,nodejs20.x,,1024,6
p0setbl-fwv949u,2022-01-04 10:10:39 UTC,No disponible,nodejs14.x,Default Lambda@Edge for Next CloudFront distribution,512,30
wallet-cxc-sls-prod-uploadDocuments,2024-06-06 20:30:53 UTC,No disponible,nodejs18.x,,1024,900
xepelin-sls-quotations-proxy-prod-updateQuotation,2025-07-22 15:07:26 UTC,2025-07-29 18:35:00 UTC,nodejs18.x,,1024,10
xepelin-sls-quotations-dev-simulateQuotation,2025-07-24 15:55:41 UTC,2025-07-24 18:35:00 UTC,nodejs18.x,,1024,10
xepelin-sls-mass-invoice-cl-staging-bill,2023-04-27 14:42:06 UTC,No disponible,nodejs16.x,,2048,480
SecretsManagermysql-rotation-function-stg,2022-01-05 19:08:26 UTC,No disponible,python3.7,Rotates a Secrets Manager secret for Amazon RDS MySQL credentials using the single user rotation strategy.,128,30
xepelin-sls-user-log-staging-save,2023-04-11 17:54:54 UTC,2025-07-29 14:35:00 UTC,nodejs16.x,,512,30
xepelin-sls-accounts-staging-getAccountActivities,2024-10-28 22:36:54 UTC,No disponible,nodejs20.x,,1024,6
xepelin-sls-funds-portal-staging-notifyCessions,2025-07-25 20:51:48 UTC,No disponible,nodejs20.x,,1024,30
xepelin-sls-funds-portal-staging-status,2025-07-25 20:51:49 UTC,No disponible,nodejs20.x,,1024,6
nb9ierm-60ghf,2022-01-04 11:11:46 UTC,No disponible,nodejs14.x,Default Lambda@Edge for Next CloudFront distribution,512,30
xepelin-sls-payments-staging-start,2021-11-11 13:25:44 UTC,No disponible,nodejs14.x,,1024,6
cyc-xaas-change-state-to-paid-sls-staging-clientChangeStateSns,2024-04-07 06:09:54 UTC,No disponible,nodejs18.x,,1024,6
xepelin-sls-funds-portal-staging-loadInvoices,2025-07-25 20:51:43 UTC,No disponible,nodejs20.x,,1024,30
xepelin-sls-funds-portal-staging-processInvoiceDeleted,2025-07-25 20:51:43 UTC,2025-07-08 14:35:00 UTC,nodejs20.x,,2048,300
xepelin-sls-wallet-events-staging-custom-resource-apigw-cw-role,2024-01-24 15:24:55 UTC,No disponible,nodejs16.x,,1024,180
xepelin-sls-receipts-stg-cl-clientReceipt,2023-04-14 14:01:41 UTC,No disponible,nodejs14.x,,1024,30
xepelin-sls-order-management-staging-processExpiredInvoices,2025-07-15 21:04:39 UTC,2025-07-29 05:35:00 UTC,nodejs14.x,,1024,600
xepelin-sls-hubspot-notifier-prod-createHubspotTask,2025-02-25 16:01:15 UTC,No disponible,nodejs18.x,,1024,30
documents-draft-cleaner-cl-rc,2024-05-15 14:03:53 UTC,2025-07-29 18:35:00 UTC,nodejs20.x,Lambda function for processing dlq tasks (database clean) on schedule in cl for rc,128,3
nb9ierm-vsr15yp,2022-01-04 11:11:19 UTC,No disponible,nodejs14.x,Image Lambda@Edge for Next CloudFront distribution,512,30
xepelin-sls-quotations-proxy-dev-createQuotation,2025-07-22 15:39:27 UTC,No disponible,nodejs18.x,,1024,10
xepelin-sls-form-buro-staging-sendCode,2025-06-04 17:53:22 UTC,No disponible,nodejs18.x,,2048,30
notifications-sender-staging-sendEmailByTemplateId,2024-12-17 13:01:59 UTC,No disponible,nodejs18.x,,1024,60
xepelin-sls-business-categorization-staging-getCategorization,2025-01-28 16:03:16 UTC,2025-07-29 18:35:00 UTC,nodejs18.x,,1024,15
xepelin-sls-funds-portal-staging-payrollAssignToGlobal,2025-07-25 20:51:47 UTC,No disponible,nodejs20.x,,1024,900
xepelin-sls-risk-module-prod-custom-resource-apigw-cw-role,2023-02-20 21:21:55 UTC,No disponible,nodejs14.x,,1024,180
wallet-cxc-sls-prod-processInBackground,2024-06-06 20:30:52 UTC,No disponible,nodejs18.x,,1024,900
xepelin-sls-accounts-cl-staging-conciliationActivities,2024-07-08 18:37:10 UTC,No disponible,nodejs20.x,,1024,6
sls-flask-api-workshop-dev-api,2022-03-31 20:09:13 UTC,No disponible,python3.8,,1024,6
xepelin-sls-bnpl-prod-createBnplOrder,2025-02-25 16:01:38 UTC,No disponible,nodejs18.x,,1024,60
xepelin-sls-transfers-staging-login,2024-01-24 15:10:37 UTC,No disponible,nodejs16.x,,1024,6
sls-payment-notification-bpc-cl-staging-status,2022-04-27 20:29:45 UTC,No disponible,nodejs14.x,,1024,6
xepelin-sls-form-buro-staging-sendEmail,2025-06-04 17:53:22 UTC,2025-07-23 21:35:00 UTC,nodejs18.x,,2048,30
xepelin-sls-bgc-workflow-prod-triggerRunSubmissionQueue,2025-06-06 01:42:18 UTC,2025-07-29 18:35:00 UTC,nodejs18.x,,1024,90
ec2_start,2021-11-19 16:32:24 UTC,No disponible,python3.7,,128,600
sls-global-invoices-risk-staging-startInvoiceRiskOnView,2021-12-17 13:31:45 UTC,No disponible,nodejs14.x,,2048,300
documents-change-status-cl-uat,2024-05-13 17:26:43 UTC,No disponible,nodejs20.x,Lambda to update the status of a document,128,3
xepelin-sls-rejected-order-notifier-prod-sendNotification,2025-02-25 16:00:44 UTC,No disponible,nodejs18.x,,1024,60
hg9h2z9-gkt9pwn,2022-01-03 16:22:32 UTC,No disponible,nodejs14.x,Default Lambda@Edge for Next CloudFront distribution,512,30
xepelin-sls-transfers-check-cl-bbe7961e1bd20edd7093f239ff4d66f6e,2024-01-24 15:18:27 UTC,No disponible,nodejs16.x,,1024,180
xepelin-sls-global-invoices-risk-stag-calculateOrder-cl,2024-02-20 14:41:15 UTC,No disponible,nodejs20.x,,1024,30
documents-draft-cleaner-cl-uat,2024-05-15 13:43:23 UTC,2025-07-29 18:35:00 UTC,nodejs20.x,Lambda function for processing dlq tasks (database clean) on schedule in cl for uat,128,3
xepelin-sls-quotations-staging-getQuotations,2025-07-28 20:04:21 UTC,No disponible,nodejs18.x,,1024,10
xepelin-sls-quotations-proxy-prod-getQuotation,2025-07-22 15:07:26 UTC,2025-07-29 19:35:00 UTC,nodejs18.x,,1024,10
xaas-01hm-hd99-qrsn-uploader,2024-03-14 12:45:58 UTC,No disponible,python3.12,,128,3
xepelin-sls-funds-portal-staging-piBulkUpdateByReceivers,2025-07-25 20:51:43 UTC,No disponible,nodejs20.x,,1024,30
xepelin-sls-funds-portal-staging-payrollInvoiceBulkUpdateStatus,2025-07-25 20:51:48 UTC,No disponible,nodejs20.x,,1024,30
xepelin-sls-get-one-click-deeplink-prod-getOneClickDeeplink,2024-03-19 16:38:53 UTC,No disponible,nodejs18.x,,1024,6
xepelin-sls-generate-pdf-staging-getSimulationPdf,2023-01-06 20:02:20 UTC,No disponible,nodejs16.x,,2048,30
rds-stop,2024-12-13 18:05:33 UTC,2025-07-29 00:35:00 UTC,python3.10,,128,600
xepelin-sls-funds-portal-staging-getAssignees,2025-07-25 20:51:43 UTC,2025-07-25 20:35:00 UTC,nodejs20.x,,1024,30
xepelin-sls-bnpl-staging-createBnplOrder,2025-02-25 15:59:02 UTC,No disponible,nodejs18.x,,1024,60
sls-global-invoices-risk-staging-startInvoiceRiskOnCreate,2021-12-17 13:31:43 UTC,No disponible,nodejs14.x,,2048,300
xepelin-sls-generate-pdf-staging-getOrderPdfClient,2023-01-06 20:02:20 UTC,No disponible,nodejs16.x,,2048,30
notifications-sender-staging-sendSlack,2024-12-17 13:02:00 UTC,No disponible,nodejs18.x,,1024,6
payment-simulation-handler-get,2022-09-09 18:12:46 UTC,No disponible,nodejs16.x,Servicio que devuelve comprobante de pago de cliente para payments.,128,60
xepelin-sls-receipts-stg-mx-clientReceipt,2023-04-14 14:01:05 UTC,No disponible,nodejs14.x,,1024,30
xepelin-sls-buro-credito-staging-getAuditBuro,2024-09-16 17:35:14 UTC,No disponible,nodejs18.x,,1024,6
xepelin-sls-bgc-workflow-staging-slackNotificationQueue,2025-06-05 23:18:57 UTC,2025-07-29 17:35:00 UTC,nodejs18.x,,1024,90
xepelin-sls-quotations-dev-generateQuotationPDF,2025-07-24 15:55:41 UTC,2025-07-21 23:35:00 UTC,nodejs18.x,,1024,15
xepelin-sls-funds-portal-staging-abstrahereCession,2025-07-25 20:51:43 UTC,No disponible,nodejs20.x,,2048,300
wallet-cxc-sls-staging-uploadDocuments,2024-04-18 17:01:54 UTC,No disponible,nodejs18.x,,1024,900
xepelin-sls-quotations-proxy-prod-getQuotations,2025-07-22 15:07:26 UTC,2025-07-29 19:35:00 UTC,nodejs18.x,,1024,10
xepelin-sls-bgc-workflow-prod-slackNotificationQueue,2025-06-06 01:42:18 UTC,2025-07-29 18:35:00 UTC,nodejs18.x,,1024,90
xepelin-sls-global-invoices-risk-stag-calculateOrder-mx,2024-02-20 14:41:15 UTC,No disponible,nodejs20.x,,1024,30
sls-payment-notification-bpc-cl-staging-depositedInvoicesFromPX,2022-04-27 20:29:45 UTC,No disponible,nodejs14.x,,1024,6
xepelin-sls-accounts-staging-checkBalance,2024-10-28 22:36:54 UTC,No disponible,nodejs20.x,,1024,6
xepelin-sls-risk-module-stg-portfolio,2023-02-08 17:55:09 UTC,2025-07-29 16:35:00 UTC,nodejs14.x,,128,900
xepelin-sls-quotations-prod-softDeleteQuotation,2025-07-29 18:53:03 UTC,No disponible,nodejs18.x,,1024,10
xepelin-sls-file-handler-get,2022-09-09 18:12:46 UTC,No disponible,nodejs16.x,Servicio que devuelve comprobante de pago de cliente para payments.,128,60
xepelin-sls-check-credit-line-staging-checkCreditLine,2025-01-21 15:36:14 UTC,2025-07-23 00:35:00 UTC,nodejs18.x,,1024,15
xepelin-sls-invoke-tgr-stag-calculateTGR,2023-03-14 20:40:16 UTC,No disponible,nodejs14.x,,1024,120
xepelin-sls-n8n-credentials-map-dev-addCredential,2025-07-29 15:21:40 UTC,2025-07-29 15:35:00 UTC,nodejs20.x,,1024,6
xepelin-sls-funds-portal-staging-processPayrollCsv,2025-07-25 20:51:43 UTC,No disponible,nodejs20.x,,2048,300
xepelin-sls-client-notifications-prod-parseSnsToSendGrid,2025-06-09 15:59:08 UTC,2025-07-29 19:35:00 UTC,nodejs18.x,,1024,10
xepelin-sls-receipts-stg-cl-status,2023-04-14 14:01:42 UTC,No disponible,nodejs14.x,,1024,30
xepelin-puertox-sls-save-movements-staging-processNewMovement,2022-12-22 14:06:44 UTC,No disponible,nodejs16.x,,2048,300
xepelin-sls-funds-portal-staging-processAbstrahereEvents,2025-07-25 20:51:43 UTC,No disponible,nodejs20.x,,2048,30
notifications-sender-prod-sendEmailByTemplateId,2024-12-19 13:14:48 UTC,No disponible,nodejs18.x,,1024,60
xepelin-sls-quotations-proxy-prod-createQuotation,2025-07-22 15:07:26 UTC,2025-07-29 19:35:00 UTC,nodejs18.x,,1024,10
xepelin-sls-generate-pdf-staging-getPaymentSimulationPdf,2023-01-06 20:02:22 UTC,No disponible,nodejs16.x,,2048,30
xepelin-sls-quotations-staging-createQuotation,2025-07-28 20:04:21 UTC,2025-07-23 21:35:00 UTC,nodejs18.x,,1024,15
xepelin-sls-receipts-staging-countPayments,2022-09-08 15:34:15 UTC,No disponible,nodejs14.x,,2048,120
xepelin-sls-global-invoices-risk-stag-enqueueExpiredOrders-mx,2024-02-20 14:41:15 UTC,2025-07-29 03:35:00 UTC,nodejs20.x,,1024,300
xepelin-sls-bnpl-dev-getBnplOrder,2025-07-22 15:37:58 UTC,No disponible,nodejs18.x,,1024,10
xepelin-sls-quotations-proxy-staging-generateQuotationPDF,2025-07-22 15:06:58 UTC,No disponible,nodejs18.x,,1024,15
xepelin-sls-funds-portal-staging-getReceiverInvoices,2025-07-25 20:51:43 UTC,No disponible,nodejs20.x,,1024,30
xepelin-sls-quotations-staging-getQuotation,2025-07-28 20:04:21 UTC,2025-07-23 18:35:00 UTC,nodejs18.x,,1024,10
documents-draft-cleaner-cl-dev,2024-06-05 15:42:53 UTC,2025-07-29 18:35:00 UTC,nodejs20.x,Lambda function for processing dlq tasks (database clean) on schedule in cl for dev,128,60
xepelin-sls-client-notifications-dev-parseSnsToSendGrid,2025-05-29 18:55:44 UTC,2025-07-23 21:35:00 UTC,nodejs18.x,,1024,10
xepelin-sls-funds-portal-staging-checkPayrollPdf,2025-07-25 20:51:49 UTC,No disponible,nodejs20.x,,1024,30
xepelin-sls-bgc-workflow-prod-workflow,2025-06-06 01:42:18 UTC,2025-07-29 19:35:00 UTC,nodejs18.x,,1024,6
network_xepel_in-cloudfront_auth,2023-03-07 19:05:44 UTC,No disponible,nodejs18.x,OKTA authentication for network.xepelin.com,128,5
xepelin-sls-quotations-staging-softDeleteQuotation,2025-07-28 20:04:21 UTC,No disponible,nodejs18.x,,1024,10
xepelin-sls-transfers-cl-productc61f5300fc5fc559bdf56290ad29b9f1,2024-01-24 15:11:49 UTC,No disponible,nodejs16.x,,1024,180
xepelin-sls-funds-portal-staging-processUpsertInvoice,2025-07-25 20:51:47 UTC,2025-07-29 15:35:00 UTC,nodejs20.x,,2048,300
CatalogoStack-LogRetentionaae0aa3c5b4d4f87b02d85b2-eaI563CsfIIB,2023-08-16 17:32:36 UTC,No disponible,nodejs18.x,,128,3
xepelin-sls-global-invoices-risk-stag-enqueueNewRiskOrders-cl,2024-02-20 14:41:15 UTC,2025-07-29 18:35:00 UTC,nodejs20.x,,1024,6
xepelin-sls-quotations-prod-generateQuotationPDF,2025-07-29 18:53:03 UTC,2025-07-29 18:35:00 UTC,nodejs18.x,,1024,15
cyc-bulk-csv-br-cl-sls-staging-bulkCSVBankReconciliation,2024-09-10 22:02:32 UTC,No disponible,nodejs20.x,,1024,900
xepelin-sls-reporteria-fondos-staging-getDashboardUrls,2021-10-22 18:00:37 UTC,No disponible,nodejs12.x,,1024,6
xepelin-sls-bnpl-prod-getBnplOrder,2025-02-25 16:01:38 UTC,2025-07-18 04:35:00 UTC,nodejs18.x,,1024,10
xepelin-sls-funds-portal-staging-getRecommendedRate,2025-07-25 20:51:43 UTC,No disponible,nodejs20.x,,1024,30
xepelin-sls-funds-portal-staging-userRegister,2025-07-25 20:51:49 UTC,No disponible,nodejs20.x,,1024,30
xepelin-sls-n8n-credentials-map-staging-updateCredentialId,2025-07-29 15:44:01 UTC,2025-07-25 20:35:00 UTC,nodejs20.x,,1024,6
xepelin-sls-quotations-staging-generateQuotationPDF,2025-07-28 20:04:21 UTC,2025-07-23 11:35:00 UTC,nodejs18.x,,1024,15
xepelin-sls-funds-portal-staging-processWSCommand,2025-07-25 20:51:47 UTC,No disponible,nodejs20.x,,2048,300
cyc-bulk-csv-br-cl-sls-staging-custom-resource-existing-s3,2024-09-10 22:02:32 UTC,No disponible,nodejs16.x,,1024,180
xepelin-sls-hubspot-notifier-staging-createHubspotTask,2025-02-25 15:58:19 UTC,No disponible,nodejs18.x,,1024,30
sls-global-invoices-risk-cl-staging-startInvoiceRiskOnView,2022-01-03 23:48:18 UTC,No disponible,nodejs14.x,,2048,300
xepelin-sls-quotations-staging-updateQuotation,2025-07-28 20:04:21 UTC,2025-07-23 11:35:00 UTC,nodejs18.x,,1024,10
xepelin-sls-generate-pdf-prod-customAuthorizerClient,2023-01-06 20:16:55 UTC,No disponible,nodejs16.x,,1024,6
x4byix-lv6b3vl,2022-01-04 10:49:10 UTC,No disponible,nodejs14.x,Image Lambda@Edge for Next CloudFront distribution,512,30
xepelin-puertox-sls-save-movements-staging-status,2022-12-22 14:06:44 UTC,No disponible,nodejs16.x,,1024,6
xepelin-sls-funds-portal-staging-userLogin,2025-07-25 20:51:48 UTC,No disponible,nodejs20.x,,1024,30
xepelin-sls-quotations-staging-getBusinessQuotations,2025-07-28 20:04:21 UTC,2025-07-29 18:35:00 UTC,nodejs18.x,,1024,10
xepelin-sls-transfers-staging-status,2024-01-24 15:11:04 UTC,No disponible,nodejs16.x,,1024,6
sls-global-invoices-risk-mx-staging-startInvoiceRiskOnCreate,2022-01-07 20:49:47 UTC,No disponible,nodejs14.x,,2048,300
xepelin-sls-business-categorization-prod-getCategorization,2025-01-28 16:09:38 UTC,2025-07-29 19:35:00 UTC,nodejs18.x,,1024,15
xepelin-sls-n8n-credentials-map-dev-updateCredentialId,2025-07-29 15:21:41 UTC,No disponible,nodejs20.x,,1024,6
xepelin-sls-deeplink-generator-dev-generateDeeplink,2025-07-22 15:39:11 UTC,No disponible,nodejs18.x,,1024,6
xepelin-sls-consult-dicom-staging-processConsult,2022-07-07 00:14:28 UTC,No disponible,nodejs14.x,,2048,300
xepelin-sns-event-tracker-dev-trackSNSEventInAmplitude,2025-03-03 18:39:32 UTC,2025-07-29 19:35:00 UTC,nodejs18.x,,1024,30
xepelin-sls-funds-portal-staging-getCustomExpirationDateV2,2025-07-25 20:51:48 UTC,No disponible,nodejs20.x,,1024,30
xepelin-sls-quotations-prod-convertQuotationToOrder,2025-07-29 18:53:03 UTC,2025-07-29 18:35:00 UTC,nodejs18.x,,1024,15
xepelin-sls-parse-sns-to-amplitude-staging-parseSnsToAmplitude,2025-02-25 15:58:19 UTC,2025-07-23 20:35:00 UTC,nodejs18.x,,1024,30
xepelin-sls-funds-portal-staging-processCessionCreated,2025-07-25 20:51:48 UTC,2025-07-15 19:35:00 UTC,nodejs20.x,,2048,300
xepelin-sls-drop-funnel-notifier-staging-sendNotification,2025-02-25 15:58:41 UTC,No disponible,nodejs18.x,,1024,60
xepelin-sls-transfers-cl-development-santander-scraper,2024-01-24 15:15:25 UTC,No disponible,nodejs16.x,,2048,30
xepelin-sls-pdf-generator-dev-generatePdf,2025-07-22 15:39:05 UTC,No disponible,nodejs18.x,,1024,6
transfer-authentication-adversely-lately-big-kangaroo,2023-09-28 21:42:01 UTC,No disponible,python3.9,,128,60
xepelin-sls-quotations-proxy-dev-getBusinessQuotations,2025-07-22 15:39:27 UTC,No disponible,nodejs18.x,,1024,10
xepelin-sls-quotations-staging-convertQuotationToOrder,2025-07-28 20:04:21 UTC,2025-07-23 21:35:00 UTC,nodejs18.x,,1024,15
xepelin-sls-funds-portal-staging-getEntityFeatures,2025-07-25 20:51:43 UTC,No disponible,nodejs20.x,,1024,30
xepelin-sls-quotations-proxy-staging-createQuotation,2025-07-22 15:06:58 UTC,2025-07-02 21:35:00 UTC,nodejs18.x,,1024,10
xepelin-sls-hubspot-notifier-dev-createHubspotTask,2025-02-24 21:33:08 UTC,No disponible,nodejs18.x,,1024,30
xepelin-sls-quotations-dev-convertQuotationToOrder,2025-07-24 15:55:41 UTC,2025-07-24 18:35:00 UTC,nodejs18.x,,1024,15
xepelin-sls-deeplink-generator-staging-generateDeeplink,2024-12-17 13:03:26 UTC,No disponible,nodejs18.x,,1024,6
xepelin-sls-quotations-proxy-prod-getBusinessQuotations,2025-07-22 15:07:26 UTC,2025-07-29 19:35:00 UTC,nodejs18.x,,1024,10
iuq3fpm-azps4p,2022-01-03 10:40:28 UTC,No disponible,nodejs14.x,Default Lambda@Edge for Next CloudFront distribution,512,30
xepelin-sls-form-buro-staging-sendCurpInfoV2,2025-06-04 17:53:22 UTC,2025-07-23 21:35:00 UTC,nodejs18.x,,2048,40
xepelin-sls-quotations-proxy-staging-updateQuotation,2025-07-22 15:06:58 UTC,No disponible,nodejs18.x,,1024,10
xepelin-sls-funds-portal-staging-getInvoice,2025-07-25 20:51:48 UTC,No disponible,nodejs20.x,,1024,30
xepelin-sns-event-tracker-prod-trackSNSEventInAmplitude,2025-03-05 13:56:18 UTC,2025-07-29 19:35:00 UTC,nodejs18.x,,1024,30
xepelin-sls-bulletin-uploader-prod-cron,2022-11-30 13:15:49 UTC,2025-07-29 12:35:00 UTC,nodejs14.x,,512,900
xepelin-sls-accounts-cl-staging-manageActivities,2024-07-08 18:37:09 UTC,2025-07-04 19:35:00 UTC,nodejs20.x,,1024,60
xepelin-sls-transfers-cl-production-transfer-analytic,2024-01-24 15:14:04 UTC,No disponible,nodejs16.x,,1024,300
xepelin-sls-global-invoices-risk-stag-processOrders-cl,2024-02-20 14:41:15 UTC,2025-07-29 19:35:00 UTC,nodejs20.x,,2048,120
wallet-cxc-sls-prod-manualTriggerUpload,2024-06-06 20:30:53 UTC,No disponible,nodejs18.x,,1024,900
docs-dw-stg_xepel_in-cloudfront_auth,2022-10-18 19:37:13 UTC,No disponible,nodejs12.x,OKTA authentication for docs-dw-stg.xepel.in,128,5
xepelin-sls-pdf-getter-dev-getPdf,2025-07-22 15:38:27 UTC,No disponible,nodejs18.x,,1024,6
xepelin-sls-transfers-cl-staging-transfer-analytic,2024-01-24 15:16:30 UTC,No disponible,nodejs16.x,,2048,900
wallet-sls-master-sit-api-dev-run,2023-12-26 20:42:31 UTC,No disponible,nodejs20.x,,1024,900
network_xepel_in-cloudfront_auth_okta,2023-03-07 18:57:24 UTC,No disponible,nodejs18.x,OKTA authentication for network.xepel.in,128,5
xepelin-sls-global-invoices-risk-stag-enqueueExpiredOrders-cl,2024-02-20 14:41:15 UTC,2025-07-29 03:35:00 UTC,nodejs20.x,,1024,300
balance-forecast-reminders-dev-sendBalanceForecastReminders,2025-07-22 15:37:31 UTC,No disponible,nodejs18.x,,1024,60
xepelin-sls-wallet-events-staging-wallet-cash-in,2024-01-24 15:25:46 UTC,No disponible,nodejs16.x,,2048,300
xepelin-sls-quotations-dev-softDeleteQuotation,2025-07-24 15:55:41 UTC,No disponible,nodejs18.x,,1024,10
xepelin-sls-form-buro-staging-sendBusinessInfo,2025-06-04 17:53:22 UTC,No disponible,nodejs18.x,,2048,30
xepelin-sls-form-buro-staging-sendZipcode,2025-06-04 17:53:22 UTC,2025-07-23 21:35:00 UTC,nodejs18.x,,2048,30
xepelin-sls-quotations-proxy-dev-generateQuotationPDF,2025-07-22 15:39:27 UTC,No disponible,nodejs18.x,,1024,15
xepelin-sls-funds-portal-staging-getReceiverInvoicesV2,2025-07-25 20:51:48 UTC,No disponible,nodejs20.x,,1024,30
xepelin-sls-generate-pdf-prod-getOrderPdfClient,2023-01-06 20:16:55 UTC,No disponible,nodejs16.x,,2048,30
xepelin-sls-quotations-proxy-staging-getQuotations,2025-07-22 15:06:58 UTC,No disponible,nodejs18.x,,1024,10
xepelin-sls-buro-credito-staging-saveAuditBuro,2024-09-17 12:40:18 UTC,No disponible,nodejs18.x,,2048,30
xepelin-sls-n8n-credentials-map-prod-addCredential,2025-07-29 18:53:37 UTC,2025-07-29 18:35:00 UTC,nodejs20.x,,1024,6
sls-xepelin-fund-recommendation-dev-api,2022-04-26 01:24:47 UTC,No disponible,python3.8,,1024,6
xepelin-sls-funds-portal-staging-getPayrollBusinessActivities,2025-07-25 20:51:47 UTC,No disponible,nodejs20.x,,1024,30
xepelin-sls-funds-portal-staging-getCessionSummary,2025-07-25 20:51:43 UTC,2025-07-29 11:35:00 UTC,nodejs20.x,,1024,30
xepelin-sls-form-buro-staging-sendStatusInfo,2025-06-04 17:53:22 UTC,No disponible,nodejs18.x,,2048,30
xepelin-sls-statement-account-generator-dev-getStatementAccounts,2024-11-27 14:13:59 UTC,No disponible,nodejs18.x,,1024,300
wallet-funds-sls-prod-getBorrowingData,2024-04-04 13:12:37 UTC,No disponible,nodejs20.x,,1024,900
xepelin-sls-quotations-proxy-staging-convertQuotationToOrder,2025-07-22 15:06:58 UTC,2025-07-09 14:35:00 UTC,nodejs18.x,,1024,15
xepelin-sls-funds-portal-staging-processTaxProviderUpdated,2025-07-25 20:51:47 UTC,2025-07-15 19:35:00 UTC,nodejs20.x,,2048,300
xepelin-sls-statement-account-dev-getStatementAccounts,2025-03-21 21:16:34 UTC,No disponible,nodejs18.x,,1024,300
xepelin-sls-accounts-staging-getBalanceByIdentifier,2024-10-28 22:36:54 UTC,No disponible,nodejs20.x,,1024,6
xepelin-sls-quotations-proxy-dev-getQuotations,2025-07-22 15:39:27 UTC,No disponible,nodejs18.x,,1024,10
xepelin-sls-quotations-dev-getBusinessQuotations,2025-07-24 15:55:41 UTC,2025-07-28 18:35:00 UTC,nodejs18.x,,1024,10
1tnc7hv-625668l,2022-01-04 11:33:11 UTC,No disponible,nodejs14.x,Default Lambda@Edge for Next CloudFront distribution,512,30
xepelin-sls-check-credit-line-dev-checkCreditLine,2025-07-22 15:38:30 UTC,2025-07-29 18:35:00 UTC,nodejs18.x,,1024,15
xepelin-sls-transfers-check-cl-bci-staging-transfers,2024-01-24 15:18:56 UTC,2025-07-29 19:35:00 UTC,nodejs16.x,,2048,30
xepelin-sls-n8n-poc-dev-getCredentialId,2025-07-22 15:37:50 UTC,2025-07-22 15:35:00 UTC,nodejs20.x,,1024,6
CatalogoStack-CustomS3AutoDeleteObjectsCustomResou-xE58ZTjlknZw,2023-08-16 17:32:36 UTC,No disponible,nodejs18.x,Lambda function for auto-deleting objects in event-catalog-prd S3 bucket.,128,900
cyc-xaas-change-state-to-paid-sls-prod-clientChangeStateSns,2024-04-07 06:27:37 UTC,No disponible,nodejs18.x,,1024,6
1qq5x74-p0xy9a,2022-01-03 19:14:09 UTC,No disponible,nodejs14.x,Default Lambda@Edge for Next CloudFront distribution,512,30
xepelin-sls-statement-account-staging-getStatementAccounts,2025-03-27 14:32:07 UTC,No disponible,nodejs18.x,,1024,300
xepelin-sls-funds-portal-staging-getReceivers,2025-07-25 20:51:47 UTC,No disponible,nodejs20.x,,1024,30
portfolio-sls-logger-staging-createItemSqs,2022-10-28 20:14:12 UTC,No disponible,nodejs16.x,,1024,6
rq2m3aa-274mx0h,2021-12-30 14:31:00 UTC,No disponible,nodejs14.x,Image Lambda@Edge for Next CloudFront distribution,512,30
xepelin-sls-quotations-prod-getBusinessQuotations,2025-07-29 18:53:03 UTC,2025-07-29 19:35:00 UTC,nodejs18.x,,1024,10
xepelin-sls-drop-funnel-notifier-dev-sendNotification,2025-02-24 21:33:30 UTC,No disponible,nodejs18.x,,1024,60
xepelin-sls-funds-portal-staging-closePayroll,2025-07-25 20:51:48 UTC,No disponible,nodejs20.x,,1024,900
test_sls_receipts,2022-02-24 18:28:06 UTC,No disponible,nodejs14.x,,128,3
xepelin-sls-one-click-opportunities-staging-getOneClickOpps,2025-02-25 15:59:09 UTC,No disponible,nodejs18.x,,1024,6
sns-slack,2023-08-10 17:30:41 UTC,No disponible,python3.8,Notificar en canal Slack via SNS Topic,128,30
xepelin-sls-funds-portal-staging-btgCallback,2025-07-25 20:51:48 UTC,2025-07-28 18:35:00 UTC,nodejs20.x,,1024,300
sls-bpc-movements-publisher-staging-processUpdateMovement,2022-09-06 16:06:59 UTC,No disponible,nodejs16.x,,2048,300
xepelin-sls-funds-portal-staging-processPfxCreated,2025-07-25 20:51:47 UTC,No disponible,nodejs20.x,,2048,300
xepelin-sls-accounts-cl-staging-getAllAccounts,2024-07-08 18:37:10 UTC,2025-07-29 14:35:00 UTC,nodejs20.x,,1024,6
xepelin-sls-transfers-cl-development-transfer-analytic,2024-01-24 15:14:52 UTC,No disponible,nodejs16.x,,1024,300
x4byix-p7efo3g,2022-01-04 10:49:36 UTC,No disponible,nodejs14.x,Default Lambda@Edge for Next CloudFront distribution,512,30
xepelin-sls-funds-portal-staging-syncInvoices,2025-07-25 20:51:49 UTC,No disponible,nodejs20.x,,1024,900
xepelin-sls-bgc-workflow-prod-automatizationsQueue,2025-06-06 01:42:20 UTC,2025-07-29 19:35:00 UTC,nodejs18.x,,1024,90
xepelin-sls-receipts-staging-custom-resource-apigw-cw-role,2022-09-08 15:34:12 UTC,No disponible,nodejs14.x,,1024,180
xepelin-sls-quotations-dev-getQuotation,2025-07-24 15:55:41 UTC,2025-07-24 18:35:00 UTC,nodejs18.x,,1024,10
wallet-xaas-client-activator-sls--prod-clientActivatorSns,2024-04-22 22:17:58 UTC,No disponible,nodejs18.x,,1024,6
xepelin-sls-funds-portal-staging-handlerWS,2025-07-25 20:51:49 UTC,No disponible,nodejs20.x,,2048,300
xepelin-sls-receipts-stg-mx-supplierReceiptsCompressed,2023-04-14 14:01:05 UTC,No disponible,nodejs14.x,,1024,30
xepelin-sls-n8n-poc-dev-updateCredentialId,2025-07-22 15:37:50 UTC,2025-07-22 14:35:00 UTC,nodejs20.x,,1024,6
xepelin-sls-quotations-prod-simulateQuotation,2025-07-29 18:53:03 UTC,2025-07-29 18:35:00 UTC,nodejs18.x,,1024,10
xepelin-sls-bgc-workflow-staging-workflow,2025-06-05 23:18:57 UTC,2025-07-29 17:35:00 UTC,nodejs18.x,,1024,6
sls-bpc-movements-publisher-staging-processCustodyByQueue,2022-09-06 16:06:55 UTC,No disponible,nodejs16.x,,2048,300
xepelin-sls-funds-portal-staging-bulkDownloadInvoicePdf,2025-07-25 20:51:48 UTC,No disponible,nodejs20.x,,2048,30
xepelin-sls-receipts-staging-clientReceiptMX,2022-09-08 15:34:19 UTC,No disponible,nodejs14.x,,2048,30
xepelin-sls-funds-portal-staging-getPayrollSummary,2025-07-25 20:51:47 UTC,No disponible,nodejs20.x,,1024,30
xepelin-sls-generate-pdf-staging-customAuthorizerClient,2023-01-06 20:02:20 UTC,No disponible,nodejs16.x,,1024,6
xepelin-sls-reporteria-fondos-ste1736b55d2ceab0cffcf3f5ef0359faa,2021-10-22 18:00:33 UTC,No disponible,nodejs12.x,,1024,180
xepelin-cob-col-sns-sender,2022-12-01 21:51:55 UTC,No disponible,nodejs18.x,,128,3
xepelin-sls-funds-portal-staging-slackCallbackAction,2025-07-25 20:51:48 UTC,No disponible,nodejs20.x,,1024,30
xepelin-sls-risk-module-stg-app,2023-02-08 17:55:10 UTC,No disponible,nodejs14.x,,128,30
Wrapper-API-Banco-Central,2024-08-27 02:30:15 UTC,No disponible,nodejs14.x,Learn about Lambda by creating an HTTP endpoint that outputs a server-side rendered web page.,128,3
xepelin-sls-accounts-cl-staging-status,2024-07-08 18:37:09 UTC,No disponible,nodejs20.x,,1024,6
xepelin-sls-quotations-prod-createQuotation,2025-07-29 18:53:03 UTC,2025-07-29 19:35:00 UTC,nodejs18.x,,1024,15
xepelin-sls-quotations-prod-getQuotation,2025-07-29 18:53:03 UTC,2025-07-29 19:35:00 UTC,nodejs18.x,,1024,10
create-collection-payer-legacy-fn,2023-02-28 13:29:53 UTC,2025-07-29 19:35:00 UTC,nodejs18.x,,1024,60
xepelin-sls-funds-staging-payerInvoices,2021-11-09 22:19:49 UTC,No disponible,nodejs12.x,,1024,6
xepelin-sls-funds-portal-staging-getPayrollInvoiceById,2025-07-25 20:51:44 UTC,No disponible,nodejs20.x,,1024,30
CatalogoStack-CustomCDKBucketDeployment8693BB64968-uFw7Own7LcPi,2023-08-16 17:32:54 UTC,No disponible,python3.9,,128,900
xepelin-sls-user-log-staging-saveByQueue,2023-04-11 17:54:55 UTC,No disponible,nodejs16.x,,512,120
rds-start,2024-12-13 18:05:39 UTC,2025-07-29 09:35:00 UTC,python3.10,,128,600
xepelin-sls-dispersion-mx-staging-custom-resource-apigw-cw-role,2022-05-26 23:22:35 UTC,No disponible,nodejs14.x,,1024,180
xepelin-sls-funds-portal-staging-updateAssignableInvoices,2025-07-25 20:51:43 UTC,No disponible,nodejs20.x,,1024,900
ACM_SLACK_NOTIFICATIONS,2025-03-04 19:42:15 UTC,2025-07-29 14:35:00 UTC,python3.13,,128,3
xepelin-sls-funds-portal-staging-notifyMissingPdfs,2025-07-25 20:51:48 UTC,No disponible,nodejs20.x,,1024,30
xepelin-sls-form-buro-staging-sendCurpInfo,2025-06-04 17:53:22 UTC,No disponible,nodejs18.x,,2048,40
xepelin-sls-syncfy-get,2022-08-18 15:28:56 UTC,No disponible,nodejs16.x,Servicio que retorna la informacion bancaria proveniente de syncfy,128,180
xepelin-sls-quotations-dev-updateQuotation,2025-07-24 15:55:41 UTC,2025-07-21 23:35:00 UTC,nodejs18.x,,1024,10
xepelin-sls-quotations-dev-createQuotation,2025-07-24 15:55:41 UTC,2025-07-24 18:35:00 UTC,nodejs18.x,,1024,15
xepelin-sls-rejected-order-notifier-dev-sendNotification,2025-02-24 21:33:08 UTC,No disponible,nodejs18.x,,1024,60
xepelin-sls-funds-portal-staging-getPayrollReceivers,2025-07-25 20:51:47 UTC,2025-07-29 11:35:00 UTC,nodejs20.x,,1024,30
business-segment-dev-segmentHttp,2023-03-23 17:42:37 UTC,No disponible,nodejs16.x,,1024,60
xepelin-sls-accounts-staging-updateAccountStatus,2024-10-28 22:36:54 UTC,No disponible,nodejs20.x,,1024,6
xepelin-sls-funds-portal-staging-getCustomExpirationDate,2025-07-25 20:51:43 UTC,No disponible,nodejs20.x,,1024,30
sls-global-invoices-risk-cl-staging-startInvoiceRiskOnCreate,2022-01-03 23:48:18 UTC,No disponible,nodejs14.x,,2048,300
documents-change-status-cl-rc,2024-05-13 17:51:15 UTC,No disponible,nodejs20.x,Lambda to update the status of a document,128,3
xepelin-sls-funds-portal-staging-payrollInvoiceBulkUpdate,2025-07-25 20:51:49 UTC,No disponible,nodejs20.x,,1024,30
xepelin-sls-form-buro-staging-recordAuthorization,2025-06-04 17:53:22 UTC,No disponible,nodejs18.x,,2048,40
v0xx0jy-4eu1qze,2022-01-04 11:57:52 UTC,No disponible,nodejs14.x,Default Lambda@Edge for Next CloudFront distribution,512,30
wallet-xaas-client-activator-sls--staging-clientActivatorSns,2024-04-23 18:39:52 UTC,No disponible,nodejs18.x,,1024,6
xepelin-sls-bulletin-uploader-prod-app,2022-11-30 13:14:32 UTC,No disponible,nodejs14.x,,512,900
lambda-event-catalog-prd,2023-08-16 17:32:30 UTC,No disponible,nodejs18.x,,128,5
xepelin-sls-accounts-staging-getAllAccounts,2024-10-28 22:36:54 UTC,No disponible,nodejs20.x,,1024,30
dw3nvs-r2szmno,2022-01-20 15:56:27 UTC,No disponible,nodejs14.x,Default Lambda@Edge for Next CloudFront distribution,512,30
xepelin-sls-transfers-cl-develope6fbbbc74bf93980f02a5b34662f8f1c,2024-01-24 15:15:53 UTC,No disponible,nodejs16.x,,1024,180
datacrowd_pipeline-dynamo-bigquery_us_prod,2025-06-04 19:56:01 UTC,2025-07-10 22:35:00 UTC,N/A,,1048,3
1tnc7hv-exsl9z2x,2022-01-04 11:32:44 UTC,No disponible,nodejs14.x,Image Lambda@Edge for Next CloudFront distribution,512,30
xepelin-sls-open-wallet-from-hubspot-staging-receive-events,2024-12-17 13:02:43 UTC,No disponible,nodejs18.x,,1024,90
xepelin-sls-quotations-prod-updateQuotation,2025-07-29 18:53:03 UTC,2025-07-29 19:35:00 UTC,nodejs18.x,,1024,10
xepelin-sls-transfers-check-cl-bci-staging-status,2024-01-24 15:20:37 UTC,No disponible,nodejs16.x,,1024,6
xepelin-sls-statement-account-prod-getStatementAccounts,2025-04-09 19:30:08 UTC,No disponible,nodejs18.x,,1024,300
xepelin-sls-credit-line-checker-dev-creditLineChecker,2024-11-21 19:10:33 UTC,No disponible,nodejs18.x,,1024,300
xepelin-sls-funds-portal-staging-getFunds,2025-07-25 20:51:48 UTC,2025-07-25 20:35:00 UTC,nodejs20.x,,1024,30
xepelin-sls-get-one-click-deeplink-dev-getOneClickDeeplink,2024-04-09 19:10:56 UTC,No disponible,nodejs18.x,,1024,60
xepelin-sls-quotations-proxy-dev-convertQuotationToOrder,2025-07-22 15:39:27 UTC,No disponible,nodejs18.x,,1024,15
xepelin-sls-risk-module-stg-custom-resource-apigw-cw-role,2023-02-08 17:55:09 UTC,No disponible,nodejs14.x,,1024,180
xepelin-sls-n8n-poc-dev-addCredential,2025-07-22 15:37:49 UTC,2025-07-21 21:35:00 UTC,nodejs20.x,,1024,6
xepelin-sls-dispersion-mx-staging-app,2022-05-26 23:23:10 UTC,No disponible,nodejs14.x,,128,6
xepelin-sls-accounts-staging-conciliationActivities,2024-10-28 22:36:54 UTC,No disponible,nodejs20.x,,1024,6
xepelin-sls-receipts-staging-status,2022-09-08 15:34:18 UTC,No disponible,nodejs14.x,,1024,6
rq2m3aa-2k4j05r,2021-12-30 16:05:31 UTC,No disponible,nodejs14.x,Default Lambda@Edge for Next CloudFront distribution,512,30
xepelin-sls-funds-portal-staging-simulationExportXLSX,2025-07-25 20:51:43 UTC,No disponible,nodejs20.x,,1024,30
xepelin-sls-global-invoices-risk-stag-enqueueNewRiskOrders-mx,2024-02-20 14:41:15 UTC,2025-07-29 18:35:00 UTC,nodejs20.x,,1024,6
xepelin-sls-funds-portal-staging-payrollSimulate,2025-07-25 20:51:48 UTC,No disponible,nodejs20.x,,1024,30
xepelin-sls-funds-portal-staging-processInvoicePdfDownload,2025-07-25 20:51:47 UTC,No disponible,nodejs20.x,,2048,300
xepelin-sls-receipts-stg-mx-status,2023-04-14 14:01:05 UTC,No disponible,nodejs14.x,,1024,30
xepelin-sls-quotations-dev-getQuotations,2025-07-24 15:55:41 UTC,No disponible,nodejs18.x,,1024,10
xepelin-sls-quotations-proxy-dev-getQuotation,2025-07-22 15:39:27 UTC,No disponible,nodejs18.x,,1024,10
xepelin-sls-receipts-stg-cl-supplierReceiptsCompressed,2023-04-14 14:01:41 UTC,No disponible,nodejs14.x,,1024,30
xepelin-sls-get-one-click-deeplink-staging-getOneClickDeeplink,2024-03-18 14:25:49 UTC,No disponible,nodejs18.x,,1024,6
cyc-event-processor-sls-staging-processEvents,2024-03-05 19:01:44 UTC,No disponible,nodejs20.x,,1024,6
cyc-templete-sls-staging-processEvents,2024-02-22 04:34:50 UTC,No disponible,nodejs20.x,,1024,6
xepelin-sls-open-wallet-from-hubspot-dev-receive-events,2025-07-22 15:38:04 UTC,No disponible,nodejs18.x,,1024,90
v0xx0jy-l90p1iq,2022-01-04 11:57:25 UTC,No disponible,nodejs14.x,Image Lambda@Edge for Next CloudFront distribution,512,30
xepelin-sls-funds-portal-staging-payrollExportXLSX,2025-07-25 20:51:49 UTC,No disponible,nodejs20.x,,1024,30
