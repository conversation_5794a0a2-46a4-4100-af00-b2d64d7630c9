name,created_date,last_invocation,runtime,description,memory_size,timeout
risk_automatic-attribute_processLDC_mx_prd,2025-06-26 19:10:38 UTC,2025-07-04 18:50:00 UTC,nodejs20.x,Example description,128,240
automation-ops_requirements_deleteInvoiceRequirements_MX_prd,2025-02-17 20:16:10 UTC,2025-07-29 18:50:00 UTC,nodejs20.x,,512,90
automation-ops_requirements_deleteOrderRequirements_CL_prd,2025-02-17 20:16:48 UTC,2025-07-29 18:50:00 UTC,nodejs20.x,,512,90
automation-ops_user-log_userLogGet_MX_prd,2025-01-06 22:27:53 UTC,No disponible,nodejs20.x,,512,30
client-application-mx-prd-authentication-migrate-user,2025-07-28 20:41:21 UTC,2025-07-29 18:50:00 UTC,nodejs20.x,,256,300
automation-ops_requirements_createOrderRequirements_MX_prd,2025-02-17 20:16:04 UTC,2025-07-29 18:50:00 UTC,nodejs20.x,,512,90
risk_sls-finantial-documents-monitor_eventProcessor_MX_prd,2025-07-28 13:55:31 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,,1024,120
automation-ops_communications-hub_subsManager_CL_prd,2024-12-02 23:22:38 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,,512,30
automation-ops_requirements-events_verifiedAccount_MX_prd,2025-01-14 22:38:31 UTC,2025-07-09 17:50:00 UTC,nodejs20.x,,256,30
automation-ops_cessions-log_cessionValidation_CL_prd,2025-01-07 16:54:35 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,,256,30
automation-ops_document-generation_documentSignatureRules_MX_prd,2025-07-28 20:08:51 UTC,2025-07-29 18:50:00 UTC,nodejs20.x,,512,30
automation-ops_user-log_userLogSave_CL_prd,2025-01-06 21:59:29 UTC,No disponible,nodejs20.x,,512,30
automation-ops_requirements_updateIsLockedOrderReqs_MX_prd,2025-02-17 20:16:16 UTC,2025-07-29 18:50:00 UTC,nodejs20.x,,512,90
client-application-mx-prd-authentication-migrate-users,2025-07-28 20:41:14 UTC,No disponible,nodejs20.x,,256,900
automation-ops_requirements-events_bankAccount_MX_prd,2025-01-14 22:38:31 UTC,2025-07-29 18:50:00 UTC,nodejs20.x,,512,30
documents-draft-cleaner-mx-prd,2024-05-17 12:23:16 UTC,2025-07-29 18:50:00 UTC,nodejs20.x,Lambda function for processing dlq tasks (database clean) on schedule in mx for prd,128,60
risk_xepelin-network_processInvoices_MX_prd,2025-07-29 13:14:17 UTC,No disponible,nodejs20.x,,1024,60
risk_embargo-risk_getEmbargoRiskData_CL_prd,2025-01-20 14:07:18 UTC,2025-07-29 18:50:00 UTC,nodejs20.x,,128,60
automation-ops_requirements_regularizeException_CL_prd,2025-02-17 20:16:19 UTC,No disponible,nodejs20.x,,512,90
automation-ops_state-order_automationStateOrder_cl_prd,2025-07-29 00:24:58 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,,512,30
automation-ops_order-confirmation-corps_getInvoices_MX_prd,2025-04-28 15:32:38 UTC,No disponible,nodejs20.x,,512,30
risk_sls-creditline-migration_partialPays_MX_prd,2025-07-18 13:57:45 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,,128,30
automation-ops_urania-api_segments_mx_prd,2025-01-14 22:37:56 UTC,2025-07-29 05:50:00 UTC,nodejs20.x,Example description,512,60
risk_compliance_opinion_status_mx_prd,2025-07-22 17:50:12 UTC,No disponible,nodejs20.x,,128,60
mytrends-cl-prd-validate-trends-changed,2024-10-29 20:41:10 UTC,2025-07-29 11:50:00 UTC,nodejs20.x,,128,30
mytrends-cl-prd-get-all-trends,2024-10-29 20:41:04 UTC,2025-07-29 11:50:00 UTC,nodejs20.x,,128,60
client-application-mx-prd-authentication-define-auth-challenge,2025-07-28 20:41:27 UTC,No disponible,nodejs20.x,,256,30
growth_monetization_relationsLoader_cl_prd,2025-07-08 18:27:02 UTC,2025-07-29 18:50:00 UTC,nodejs20.x,Example description,2048,600
risk_sls-buro-credito_status_MX_prd,2025-03-12 13:20:10 UTC,No disponible,nodejs20.x,,128,90
risk_sls-creditline-migration_kafkaDebtCreated_CL_prd,2025-07-24 18:23:31 UTC,2025-07-29 18:50:00 UTC,nodejs20.x,,512,60
risk_automatic-attribute_consultComplianceMX_mx_prd,2025-06-26 19:40:06 UTC,2025-07-29 18:50:00 UTC,nodejs20.x,Example description,128,120
automation-ops_requirements_getExceptionById_CL_prd,2025-02-17 20:16:19 UTC,2025-07-28 16:50:00 UTC,nodejs20.x,,512,90
growth_monetization_autokamActionsManager_mx_prd,2025-07-25 20:11:46 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,Example description,512,180
growth_acq-center_hubspotWebhook_MX_prd,2025-06-25 19:17:56 UTC,No disponible,nodejs20.x,Example description,128,180
automation-ops_requirements-events_verifiedInvoice_MX_prd,2025-05-23 20:18:36 UTC,2025-07-29 18:50:00 UTC,nodejs20.x,,512,30
growth_monetization_eventHandler-OrderActive_mx_prd,2025-07-14 19:01:35 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,Example description,512,180
risk_xepelin-network_readBusiness_MX_prd,2025-03-21 15:55:35 UTC,No disponible,nodejs20.x,,1024,900
risk_automatic-attribute_sendSlackRequeryErrors_cl_prd,2025-07-29 17:59:01 UTC,2025-07-29 18:50:00 UTC,nodejs20.x,Example description,128,60
risk_xepelin-network_processBusiness_CL_prd,2025-07-29 13:14:25 UTC,No disponible,nodejs20.x,,1024,60
automation-ops_requirements-events_noHoldInvoices_CL_prd,2025-01-02 17:02:16 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,,256,30
mytrends-mx-prd-validate-trends-in-alert,2024-10-29 20:42:25 UTC,No disponible,nodejs20.x,,128,30
collections_mailing_partialPayCalculateMX_mx_prd,2025-02-19 12:24:07 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,Calculate the partial payment for MX,128,30
growth_monetization_scrapperPrioritizer_cl_prd,2025-06-18 18:35:22 UTC,2025-07-29 18:50:00 UTC,nodejs20.x,Example description,512,29
client-application-cl-prd-authentication-upsert-user,2025-07-28 20:38:58 UTC,2025-07-29 18:50:00 UTC,nodejs20.x,,256,300
growth_monetization_hubspotEventHandler_mx_prd,2025-07-15 20:31:41 UTC,No disponible,nodejs20.x,Example description,512,180
automation-ops_communications-hub_subsMessage_MX_prd,2025-01-14 22:38:13 UTC,2025-07-29 17:50:00 UTC,nodejs20.x,,256,30
automation-ops_requirements-events_assignableInvoice_CL_prd,2025-07-10 20:50:36 UTC,2025-07-29 18:50:00 UTC,nodejs20.x,,512,30
automation-ops_requirements-events_invoiceCeded_CL_prd,2025-01-03 00:14:10 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,,256,30
conciliation_sls-accounts_updateAccountStatus_cl_prd,2025-01-24 02:04:19 UTC,No disponible,nodejs20.x,Example description,128,30
core-sofipo_operational-reports_statements-account-data_mx_prd,2025-05-23 18:55:16 UTC,2025-07-01 10:50:00 UTC,nodejs20.x,Lambda function responsible for generating data for account statements.,256,240
risk_xepelin-network_saveResultsNetwork_CL_prd,2025-06-26 19:32:35 UTC,2025-07-29 18:50:00 UTC,nodejs20.x,,1024,60
automation-ops_requirements_deleteInvoiceRequirements_CL_prd,2025-07-24 14:58:59 UTC,2025-07-29 16:50:00 UTC,nodejs20.x,,512,90
collections_mailing_prepareEmails_cl_prd,2025-05-19 20:28:04 UTC,No disponible,nodejs20.x,Get debts and prepare the information to send the emails,1024,900
core-sofipo_file-processor_operational-reports_mx_prd,2025-02-11 18:41:58 UTC,2025-07-29 07:50:00 UTC,nodejs20.x,File processor Lambda,128,180
growth_monetization_newActivationEventFilter_cl_prd,2025-06-18 18:35:27 UTC,No disponible,nodejs20.x,Example description,512,29
core-sofipo_notification-center_banking-account-notify_mx_prd,2025-06-19 14:30:28 UTC,No disponible,nodejs20.x,Lambda function responsible for sending account notifications to core banking.,256,240
collections_mailing_saveCsvManagements_cl_prd,2025-05-19 20:28:28 UTC,No disponible,nodejs20.x,Save csv managements,128,60
risk_embargo-risk_dlqSlackNotifier_CL_prd,2025-01-20 14:07:04 UTC,No disponible,nodejs20.x,,512,60
risk_xepelin-network_readInvoices_CL_prd,2025-03-21 15:55:54 UTC,No disponible,nodejs20.x,,1024,900
mytrends-cl-prd-set-new-suppliers,2024-10-29 20:41:34 UTC,2025-07-29 11:50:00 UTC,nodejs20.x,,128,30
debt_debt_replication_CL_prd_fn,2024-12-04 14:11:20 UTC,No disponible,nodejs20.x,,128,900
risk_crif_app_CL_prd,2025-04-24 14:44:47 UTC,2025-07-29 18:50:00 UTC,nodejs20.x,,128,60
automation-ops_requirements_getInvoiceRequirements_CL_prd,2025-02-17 20:16:18 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,,512,90
risk_sls-creditline-migration_creditlineProcessEvents_CL_prd,2025-07-24 15:11:29 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,,128,30
automation-ops_cessions-log_cessionManagement_CL_prd,2025-01-07 16:54:35 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,,512,30
debt_debt_replication_MX_prd_fn,2025-07-22 16:49:01 UTC,No disponible,nodejs20.x,,128,900
automation-ops_check-order_checkOrderTransfers_CL_prd,2025-07-10 21:20:21 UTC,2025-07-29 18:50:00 UTC,nodejs20.x,,512,30
core-sofipo_notification-center_customer-block_mx_prd,2025-06-19 14:30:36 UTC,No disponible,nodejs20.x,Lambda function responsible for send customer block notification to slack.,256,240
risk_automatic-attribute_processOrder_cl_prd,2025-07-29 17:58:43 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,Example description,1024,100
risk_siger_service_bulkEnqueueStakeholder_mx_prd,2024-09-09 18:45:50 UTC,No disponible,nodejs20.x,,128,60
risk_variables-api_variableOrchestrator_MX_prd,2025-05-20 20:10:43 UTC,No disponible,nodejs20.x,,2048,840
automation-ops_document-generation_documentGeneration_MX_prd,2024-12-13 17:24:52 UTC,2025-07-29 18:50:00 UTC,nodejs20.x,,1024,60
risk_sls-creditline-migration_processKafkaEvents_CL_prd,2025-07-24 18:23:39 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,,512,60
risk_automatic-attribute_status_mx_prd,2025-06-26 18:59:44 UTC,No disponible,nodejs20.x,Example description,128,15
client-application-cl-prd-authentication-pre-token-generation,2025-07-28 20:38:48 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,,256,300
collections_mailing_sendEmail_cl_prd,2025-05-19 20:31:45 UTC,No disponible,nodejs20.x,Last step to send the email,128,30
paas-cloudtrail-elb-github-createListenerLambda-9d92276,2024-10-31 20:53:14 UTC,2025-07-21 19:50:00 UTC,provided.al2,,128,3
automation-ops_user-log_userLogSave_MX_prd,2025-01-06 22:27:47 UTC,No disponible,nodejs20.x,,512,30
growth_monetization_eventHandler-OrderToDeposit_mx_prd,2025-07-14 19:01:41 UTC,2025-07-29 18:50:00 UTC,nodejs20.x,Example description,512,180
core-sofipo_operational-reports_tritium-data-loader_mx_prd,2025-05-23 18:55:12 UTC,2025-07-29 07:50:00 UTC,nodejs20.x,Lambda function responsible for save tritium data in the database,256,290
growth_monetization_growthSignalsSaver_cl_prd,2025-07-08 21:30:21 UTC,2025-07-29 18:50:00 UTC,nodejs20.x,Example description,512,300
risk_automatic-attribute_bulkServiceOrder_mx_prd,2025-07-14 13:38:57 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,Example description,1024,150
automation-ops_requirements_getRequirementMetadata_MX_prd,2025-02-17 20:16:14 UTC,2025-07-29 18:50:00 UTC,nodejs20.x,,512,90
debt_debt_inactivate_MX_prd_fn,2025-07-22 16:49:00 UTC,2025-07-25 23:50:00 UTC,nodejs20.x,,128,30
growth_monetization_resourcesDynamo_mx_prd,2025-06-18 18:35:32 UTC,No disponible,nodejs20.x,Example description,128,60
growth_monetization_tagBusinesses_cl_prd,2025-07-14 19:02:17 UTC,No disponible,nodejs20.x,Example description,512,29
automation-ops_order-confirmation_getAnalyst_CL_prd,2025-03-20 16:44:39 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,,512,30
sales-systems_growth-partners-client_partners_MX_prd,2024-07-22 22:57:39 UTC,No disponible,nodejs20.x,,128,270
core-sofipo_operational-reports_accounting-access_mx_prd,2025-05-23 18:55:12 UTC,2025-07-29 09:50:00 UTC,nodejs20.x,Lambda function responsible for access data to generate accounting.,256,240
risk_xepelin-network_getInvoices_MX_prd,2025-06-26 19:33:00 UTC,No disponible,nodejs20.x,,2048,840
automation-ops_requirements-events_regulatoryLimit_MX_prd,2025-03-20 16:16:18 UTC,No disponible,nodejs20.x,,512,30
risk_automatic-attribute_kafka2SqsOriginationOrderCreated_cl_prd,2025-07-29 17:58:59 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,Example description,1024,100
risk_xtree_cotizacionesWebhook_cl_prd,2025-05-22 13:10:39 UTC,No disponible,nodejs20.x,Example description,1024,90
risk_sls-creditline-migration_updateMldcBalance_MX_prd,2025-07-14 19:07:00 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,,128,60
documents-change-status-mx-prd,2024-05-17 12:20:37 UTC,No disponible,nodejs20.x,Lambda to update the status of a document,128,60
automation-ops_requirements_updateIsLockedOrderReqs_CL_prd,2025-07-24 14:59:27 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,,512,90
client-application-mx-prd-wallet-processors-transfers,2025-07-28 20:40:25 UTC,2025-07-28 15:50:00 UTC,nodejs20.x,,256,300
growth_monetization_growthSignalsSaver_mx_prd,2025-07-08 21:30:04 UTC,2025-07-28 15:50:00 UTC,nodejs20.x,Example description,512,300
risk_yes-but_getBusinessVariables_MX_prd,2024-10-24 15:27:35 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,,128,30
collections_mailing_saveCsvManagements_mx_prd,2025-02-19 12:24:11 UTC,2025-07-23 21:50:00 UTC,nodejs20.x,Save csv managements,128,60
automation-ops_order-confirmation-corps_parseInvoices_CL_prd,2025-04-15 16:26:02 UTC,2025-07-28 18:50:00 UTC,nodejs20.x,,512,30
risk_pipeline-mx_updateCreditLineAnalystStatus_MX_prd,2025-04-03 18:50:33 UTC,2025-07-17 17:50:00 UTC,nodejs20.x,,128,30
automation-ops_requirements_updateException_MX_prd,2025-02-17 20:16:15 UTC,No disponible,nodejs20.x,,512,90
risk_yes-but_processGandalfTable_MX_prd,2024-10-24 15:27:37 UTC,2025-07-29 11:50:00 UTC,nodejs20.x,,128,30
risk_xepelin-network_saveResultsNetwork_MX_prd,2025-03-21 15:55:35 UTC,2025-07-29 08:50:00 UTC,nodejs20.x,,1024,60
risk_automatic-attribute_status_cl_prd,2025-07-29 17:58:39 UTC,No disponible,nodejs20.x,Example description,128,3
risk_automatic-attribute_pymeBuroComplianceMX_mx_prd,2025-06-27 13:45:22 UTC,2025-07-21 17:50:00 UTC,nodejs20.x,Example description,128,900
automation-ops_user-log_userLogSaveByQueue_MX_prd,2025-01-06 22:27:41 UTC,No disponible,nodejs20.x,,512,30
automation-ops_order-confirmation-corps_appendInvoice_CL_prd,2025-05-20 18:58:03 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,,512,30
client-application-mx-prd-authentication-check-sync-user,2025-07-28 20:41:26 UTC,No disponible,nodejs20.x,,256,900
automation-ops_requirements_createInvoiceRequirements_CL_prd,2025-02-17 20:16:20 UTC,2025-07-29 18:50:00 UTC,nodejs20.x,,512,90
risk_yes-but_updateBusinessVariableFiles_MX_prd,2024-11-07 14:48:08 UTC,2025-07-29 16:50:00 UTC,nodejs20.x,,128,30
risk_automatic-attribute_enqueueTgrDebt_cl_prd,2025-07-29 17:58:42 UTC,2025-07-19 18:50:00 UTC,nodejs20.x,Example description,1024,60
growth_monetization_tagBusinesses_mx_prd,2025-07-14 19:01:28 UTC,No disponible,nodejs20.x,Example description,128,29
debt_debt_inactivate_CL_prd_fn,2024-12-04 14:11:22 UTC,2025-07-29 16:50:00 UTC,nodejs20.x,,128,30
risk_xepelin-network_getBusiness_CL_prd,2025-03-21 15:55:53 UTC,No disponible,nodejs20.x,,2048,900
risk_automatic-attribute_bulkEnqueue_cl_prd,2025-07-29 17:59:14 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,Example description,1024,60
risk_xepelin-network_readInvoices_MX_prd,2025-03-21 15:55:36 UTC,No disponible,nodejs20.x,,1024,900
risk_sls-creditline-recurrence_processBusiness_MX_prd,2025-04-24 18:09:01 UTC,2025-07-29 08:50:00 UTC,nodejs20.x,,1024,120
risk_sls-creditline-increase-request_processRequest_MX_prd,2025-03-14 13:25:22 UTC,2025-07-25 17:50:00 UTC,nodejs20.x,,1024,120
automation-ops_order-confirmation_processorAnalyst_CL_prd,2025-07-10 20:27:29 UTC,2025-07-29 18:50:00 UTC,nodejs20.x,,256,30
risk_automatic-attribute_generateReportFileOnDemand_mx_prd,2025-06-26 18:59:52 UTC,2025-07-28 15:50:00 UTC,nodejs20.x,Example description,128,300
risk_sigfe_loadSigfeInvoices_CL_prd,2024-10-09 12:33:24 UTC,No disponible,nodejs20.x,,128,120
automation-ops_document-generation_orchestrator_MX_prd,2025-04-08 21:58:57 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,,512,30
mytrends-mx-prd-save-trends-in-alert,2024-10-29 20:42:55 UTC,No disponible,nodejs20.x,,128,30
client-application-mx-prd-documents-generate-pdf,2025-07-28 20:40:03 UTC,2025-07-29 18:50:00 UTC,nodejs18.x,,256,300
risk_sls-creditline-migration_calculateBalance_CL_prd,2025-07-18 13:57:53 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,,128,60
risk_sls-creditline-migration_kafkaOrderApproved_CL_prd,2025-07-24 18:23:38 UTC,2025-07-29 18:50:00 UTC,nodejs20.x,,512,60
debt_update-expired_MX_prd_trigger_fn,2025-07-22 16:48:53 UTC,2025-07-29 05:50:00 UTC,nodejs20.x,,128,180
risk_yes-but_enqueueExpiredBusinessVariables_MX_prd,2024-10-24 15:27:36 UTC,2025-07-28 23:50:00 UTC,nodejs20.x,,128,30
growth_monetization_autokamPendingResponsesEvents_cl_prd,2025-07-08 21:30:02 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,Example description,512,180
growth_monetization_metaWebhook_cl_prd,2025-07-15 13:35:18 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,Example description,512,180
growth_monetization_eventHandler-OrderInvoiceAdded_cl_prd,2025-07-14 19:02:03 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,Example description,512,180
risk_sls-creditline-migration_creditlineProcessEvents_MX_prd,2025-07-24 15:11:10 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,,128,30
sales-systems_hubspot-etl_MX_prod,2025-05-22 14:35:56 UTC,2025-07-29 18:50:00 UTC,nodejs20.x,Proyecto para conectarse con Hubspot,1024,250
client-application-cl-prd-authentication-define-auth-challenge,2025-07-28 20:39:02 UTC,No disponible,nodejs20.x,,256,30
collections_mailing_sendEmail_mx_prd,2025-07-25 14:11:52 UTC,2025-07-29 15:50:00 UTC,nodejs20.x,Last step to send the email,128,30
risk_xtree_apiCotizacion_cl_prd,2025-05-22 13:10:43 UTC,No disponible,nodejs20.x,Example description,1024,30
risk_xtree_updateInvoices_cl_prd,2025-07-08 13:58:17 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,Example description,1024,120
debt_payment_notifications_MX_prd_fn,2025-07-22 17:06:18 UTC,No disponible,nodejs20.x,,128,30
automation-ops_requirements-events_verifiedInvoice_CL_prd,2025-05-23 20:24:43 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,,512,30
risk_xtree_processInvoiceEvent_cl_prd,2025-07-08 13:04:10 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,Example description,1024,120
automation-ops_kams-management_getKamsByBusinessId_CL_prd,2024-11-20 00:05:05 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,,256,30
risk_compliance_opinion_loadCompliaceOpinionData_mx_prd,2025-07-22 17:50:12 UTC,2025-07-29 18:50:00 UTC,nodejs20.x,,128,120
risk_variables-api_variableErrorsCollection_CL_prd,2025-05-20 20:10:38 UTC,No disponible,nodejs20.x,,2048,840
risk_compliance_opinion_saveComplianceOpinion_mx_prd,2025-07-22 17:50:18 UTC,2025-07-29 18:50:00 UTC,nodejs20.x,,1024,60
risk_sls-creditline-migration_calculateBalanceV2_MX_prd,2025-07-24 15:10:56 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,,128,60
core-sofipo_tritium-webhook_test-login-lambda_mx_prd,2025-07-24 16:51:19 UTC,2025-07-24 16:50:00 UTC,nodejs20.x,Test lambda function that calls the Tritium login endpoint through AWS Private Link for testing connectivity.,256,30
growth_monetization_pdfGenerator_cl_prd,2025-06-10 18:39:10 UTC,No disponible,nodejs20.x,Example description,512,60
risk_automatic-attribute_logEngineResponses_mx_prd,2025-06-26 18:59:50 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,Example description,128,900
risk_variables-api_variableErrorsCollection_MX_prd,2025-05-20 20:10:55 UTC,No disponible,nodejs20.x,,2048,840
risk_xtree_notifyErrorsToSlack_mx_prd,2025-05-22 13:10:27 UTC,2025-07-18 17:50:00 UTC,nodejs20.x,Example description,1024,30
sales-system_mvp-proxy_serverGlobalQuery_mx_prd,2025-07-22 14:08:25 UTC,No disponible,nodejs20.x,Example description,128,3
growth_monetization_autokamLangfuseEtl_cl_prd,2025-07-29 13:24:52 UTC,2025-07-29 11:50:00 UTC,nodejs20.x,Example description,1024,180
debt_debt_q_CL_prd_fn,2024-12-04 14:11:21 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,,128,30
automation-ops_kams-management_kamAssignmentRegistry_MX_prd,2024-12-12 22:54:05 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,,256,30
growth_monetization_financialData_mx_prd,2025-07-08 21:29:37 UTC,2025-07-29 11:50:00 UTC,nodejs20.x,Example description,128,29
risk_xtree_updateLDCEvent_cl_prd,2025-05-22 13:10:38 UTC,2025-07-28 15:50:00 UTC,nodejs20.x,Example description,1024,60
automation-ops_requirements-events_verifiedAccount_CL_prd,2025-01-02 17:02:15 UTC,2025-07-09 17:50:00 UTC,nodejs20.x,,256,30
risk_siger_service_expireUnprocessedRequestMX_mx_prd,2024-09-09 18:45:51 UTC,2025-07-29 05:50:00 UTC,nodejs20.x,,128,120
debt_debt_service_proxy_MX_prd_fn,2025-07-22 16:48:52 UTC,2025-07-25 15:50:00 UTC,nodejs20.x,,128,30
automation-ops_requirements_updateInvoiceRequirements_MX_prd,2025-02-17 20:16:11 UTC,2025-07-29 18:50:00 UTC,nodejs20.x,,512,90
risk_compliance_opinion_getComplianceOpinion_mx_prd,2025-07-22 17:50:19 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,,512,60
risk_xtree_invoiceFail_cl_prd,2025-05-22 13:10:43 UTC,No disponible,nodejs20.x,Example description,1024,3
growth_monetization_slackOpportunitiesSender_cl_prd,2025-07-14 19:02:05 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,Example description,512,29
risk_xtree_logEngineResponses_mx_prd,2025-05-22 13:10:27 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,Example description,1024,60
automation-ops_requirements_getRequirementMetadata_CL_prd,2025-02-17 20:16:28 UTC,2025-07-29 18:50:00 UTC,nodejs20.x,,512,90
risk_xtree_updateInvoices_mx_prd,2025-07-08 13:58:18 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,Example description,1024,120
collections_contacts-api_createBusinessContact_mx_prd,2024-06-12 16:29:37 UTC,2025-07-15 17:50:00 UTC,nodejs20.x,Example description,128,30
automation-ops_requirements-events_rejectedInvoice_CL_prd,2025-01-02 17:02:15 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,,256,30
automation-ops_order-confirmation_recieveEmailWebhook_CL_prd,2025-07-10 20:27:23 UTC,No disponible,nodejs20.x,,256,30
automation-ops_requirements-events_manageDocuments_MX_prd,2025-03-20 16:12:33 UTC,No disponible,nodejs20.x,,512,30
risk_sls-creditline-migration_kafkaPaymentAssigned_MX_prd,2025-07-24 18:23:40 UTC,No disponible,nodejs20.x,,512,60
risk_automatic-attribute_logEngineResponses_cl_prd,2025-07-29 17:58:40 UTC,2025-07-29 18:50:00 UTC,nodejs20.x,Example description,128,900
risk_sls-creditline-migration_calculateBalanceV2_CL_prd,2025-07-24 15:11:30 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,,128,60
growth_monetization_eventHandler-OrderApproved_mx_prd,2025-07-14 19:01:36 UTC,2025-07-29 18:50:00 UTC,nodejs20.x,Example description,512,180
client-application-cl-prd-authentication-sync-user-event,2025-07-28 20:39:03 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,,256,30
growth_monetization_autokamActionsManager_cl_prd,2025-07-25 20:12:38 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,Example description,512,180
risk_embargo-risk_saveEmbargoRisk_CL_prd,2025-01-20 15:50:43 UTC,2025-07-19 18:50:00 UTC,nodejs20.x,,1024,300
automation-ops_requirements_regularizeException_MX_prd,2025-02-17 20:16:14 UTC,2025-07-22 20:50:00 UTC,nodejs20.x,,512,90
client-application-cl-prd-authentication-create-user,2025-07-28 20:38:51 UTC,No disponible,nodejs20.x,,256,300
core-sofipo_tritium-webhook_tritium-customer-notification_mx_prd,2025-07-24 16:35:40 UTC,No disponible,nodejs20.x,Lambda function responsible for send customer block notification to slack.,256,240
client-application-mx-prd-authentication-remove-role,2025-07-28 20:41:17 UTC,No disponible,nodejs20.x,,256,30
automation-ops_requirements_filterOrderRequirements_MX_prd,2025-02-17 20:16:06 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,,512,90
automation-ops_requirements_createOrderRequirements_CL_prd,2025-07-24 14:58:52 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,,512,90
risk_pipeline-mx_processUpdateAssignAnalyst_MX_prd,2025-04-07 18:58:23 UTC,2025-07-29 18:50:00 UTC,nodejs20.x,,128,180
risk_xepelin-network_getInvoices_CL_prd,2025-03-21 15:55:53 UTC,2025-07-29 18:50:00 UTC,nodejs20.x,,2048,840
growth_monetization_eventHandler-OrderAppealed_cl_prd,2025-07-14 19:01:56 UTC,2025-07-29 18:50:00 UTC,nodejs20.x,Example description,512,180
risk_siger_service_enqueuePeriodicalCreditLine_mx_prd,2024-09-09 18:45:50 UTC,2025-07-17 05:50:00 UTC,nodejs20.x,,128,60
automation-ops_requirements-events_rejectedInvoice_MX_prd,2025-01-14 22:38:32 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,,256,30
risk_xtree_processLDCEvent_mx_prd,2025-07-29 15:39:45 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,Example description,1024,120
growth_monetization_autokamCustomerTags_cl_prd,2025-07-14 15:14:44 UTC,2025-07-29 10:50:00 UTC,nodejs20.x,Example description,512,180
client-application-mx-prd-authentication-verify-auth-challenge,2025-07-28 20:41:19 UTC,No disponible,nodejs20.x,,256,30
core-sofipo_operational-reports_statements-access_mx_prd,2025-05-23 18:55:13 UTC,No disponible,nodejs20.x,Lambda function responsible for access data to generate account statements.,256,240
automation-ops_requirements-events_validationKam_MX_prd,2025-05-14 15:41:14 UTC,2025-07-29 18:50:00 UTC,nodejs20.x,,256,30
automation-ops_kams-management_kamAssignmentRegistry_CL_prd,2024-11-21 21:03:04 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,,256,30
risk_xepelin-network_readBusiness_CL_prd,2025-03-21 15:55:53 UTC,No disponible,nodejs20.x,,1024,900
conciliation_sls-accounts_getProfileId_cl_prd,2025-01-24 02:04:20 UTC,No disponible,nodejs20.x,Example description,128,30
risk_automatic-attribute_kafka2SqsOrderInvoiceAdded_cl_prd,2025-07-29 17:59:00 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,Example description,1024,100
debt_debt_service_proxy_CL_prd_fn,2024-12-04 14:11:21 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,,128,30
automation-ops_sync-gateway_authorizer_MX_prd,2025-01-14 22:37:34 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,,512,30
growth_monetization_eventHandler-OrderReviewed_mx_prd,2025-07-14 19:01:29 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,Example description,512,180
automation-ops_user-log_userLogGet_CL_prd,2025-01-06 22:03:12 UTC,No disponible,nodejs20.x,,512,30
mytrends-mx-prd-validate-trends-changed,2024-10-29 20:42:07 UTC,2025-07-29 14:50:00 UTC,nodejs20.x,,128,30
client-application-mx-prd-authentication-assign-role,2025-07-28 20:41:25 UTC,2025-07-29 15:50:00 UTC,nodejs20.x,,256,30
risk_variables-api_apiEndpoint_MX_prd,2025-05-20 20:10:49 UTC,No disponible,nodejs20.x,,2048,840
risk_sls-creditline-migration_kafkaOrderApproved_MX_prd,2025-07-24 18:23:33 UTC,2025-07-29 18:50:00 UTC,nodejs20.x,,512,60
collections_contacts-api_markToSendEmailContact_mx_prd,2024-06-12 16:29:38 UTC,No disponible,nodejs20.x,Example description,128,30
risk_automatic-attribute_enqueueBulkTGREmbargoRisk_cl_prd,2025-07-29 17:58:41 UTC,2025-07-28 23:50:00 UTC,nodejs20.x,Example description,128,900
mytrends-cl-prd-validate-trends-in-alert,2024-10-29 20:41:28 UTC,2025-07-29 11:50:00 UTC,nodejs20.x,,128,30
risk_automatic-attribute_kafka2SqsOriginationOrderCreated_mx_prd,2025-06-26 19:39:56 UTC,No disponible,nodejs20.x,Example description,1024,100
automation-ops_download-xml_extractionInvoice_mx_prd,2025-01-14 22:38:11 UTC,No disponible,nodejs20.x,Example description,256,30
mytrends-cl-prd-set-new-suppliers-blocklisted,2024-10-29 20:41:22 UTC,2025-07-29 11:50:00 UTC,nodejs20.x,,128,30
conciliation_sls-accounts_getBalanceByIdentifier_cl_prd,2025-01-24 02:04:18 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,Example description,128,30
risk_sls-creditline-migration_kafkaPaymentAssigned_CL_prd,2025-07-24 18:23:31 UTC,No disponible,nodejs20.x,,512,60
risk_automatic-attribute_enqueueFileOnDemandCL_cl_prd,2025-07-29 17:59:15 UTC,No disponible,nodejs20.x,Example description,128,900
sales-system_mvp-proxy_serverGlobalQuery_cl_prd,2025-07-22 14:08:20 UTC,No disponible,nodejs20.x,Example description,128,3
core-sofipo_notification-center_layout-event_mx_prd,2025-06-19 14:30:36 UTC,No disponible,nodejs20.x,Lambda function responsible for processing messages from the queue.,128,180
risk_sls-creditline-migration_updateMldcBalance_CL_prd,2025-07-15 14:56:32 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,,128,60
risk_xtree_notifyLDCErrorsToSlack_cl_prd,2025-05-22 13:10:43 UTC,No disponible,nodejs20.x,Example description,1024,30
client-application-mx-prd-authentication-create-user,2025-07-28 20:41:18 UTC,No disponible,nodejs20.x,,256,300
growth_monetization_eventHandler-OrderApproved_cl_prd,2025-07-14 19:02:07 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,Example description,512,180
risk_embargo-risk_enqueue_CL_prd,2025-01-20 14:06:51 UTC,2025-07-19 18:50:00 UTC,nodejs20.x,,128,60
risk_siger_service_statust_mx_prd,2024-09-09 18:45:51 UTC,No disponible,nodejs20.x,,128,60
risk_automatic-attribute_enqueueComplianceCL_cl_prd,2025-07-29 17:59:02 UTC,No disponible,nodejs20.x,Example description,128,300
mytrends-mx-prd-send-email-on-trend-in-alert,2024-10-29 20:42:37 UTC,No disponible,nodejs20.x,,128,60
mytrends-mx-prd-remove-trends-without-alert,2024-10-29 20:42:43 UTC,No disponible,nodejs20.x,,128,30
automation-ops_requirements_getExceptionById_MX_prd,2025-02-17 20:16:07 UTC,2025-07-29 18:50:00 UTC,nodejs20.x,,512,90
client-application-mx-prd-authentication-upsert-user,2025-07-28 20:41:24 UTC,2025-07-29 18:50:00 UTC,nodejs20.x,,256,300
growth_monetization_eventHandler-OrderRejected_mx_prd,2025-07-14 19:01:29 UTC,2025-07-29 18:50:00 UTC,nodejs20.x,Example description,512,180
risk_xtree_logEngineResponses_cl_prd,2025-05-22 13:10:35 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,Example description,1024,60
risk_xtree_processInvoices_mx_prd,2025-07-29 15:39:45 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,Example description,2048,720
automation-ops_sync-gateway_authorizer_CL_prd,2024-12-02 23:22:33 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,,512,30
risk_variables-api_variableWebhook_CL_prd,2025-05-16 21:31:29 UTC,No disponible,nodejs20.x,,2048,840
risk_sls-creditline-migration_kafkaOrderToDeposit_MX_prd,2025-07-24 18:23:32 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,,512,60
risk_xtree_processInvoiceEvent_mx_prd,2025-07-08 13:04:01 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,Example description,1024,120
mytrends-mx-prd-process-page,2024-10-29 20:42:13 UTC,2025-07-29 14:50:00 UTC,nodejs20.x,,128,60
client-application-cl-prd-authentication-create-auth-challenge,2025-07-28 20:38:54 UTC,No disponible,nodejs20.x,,256,30
growth_monetization_metaWebhook_mx_prd,2025-07-15 14:09:18 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,Example description,512,180
automation-ops_communications-hub_subsManager_MX_prd,2025-01-14 22:38:20 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,,512,30
conciliation_sls-accounts_conciliationActivities_cl_prd,2025-01-24 02:04:19 UTC,No disponible,nodejs20.x,Example description,128,30
debt_server_global_proxy_CL_prd_fn,2024-12-04 14:11:27 UTC,No disponible,nodejs20.x,,128,30
automation-ops_state-order_automationNotifyRequirements_mx_prd,2025-07-29 00:29:35 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,,512,30
client-application-mx-prd-authentication-sync-user-event,2025-07-28 20:41:28 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,,256,30
aws-controltower-NotificationForwarder,2023-09-07 20:53:39 UTC,2025-07-29 18:50:00 UTC,python3.9,SNS message forwarding function for aggregating account notifications.,128,60
automation-ops_requirements-events_orderCreatedEvent_MX_prd,2025-07-10 20:44:53 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,,512,30
risk_sls-creditline-migration_kafkaOrderToDeposit_CL_prd,2025-07-24 18:23:38 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,,512,60
client-application-mx-prd-authentication-pre-token-generation,2025-07-28 20:41:16 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,,256,300
automation-ops_segment-update_api_cl_prd,2024-10-30 16:29:29 UTC,2025-07-29 05:50:00 UTC,nodejs20.x,Example description,128,90
debt_rate_event_forwarding_MX_prd_fn,2025-07-22 17:06:37 UTC,No disponible,nodejs20.x,,128,30
risk_creditline-manager_app_CL_prd,2025-04-23 17:14:55 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,,128,30
risk_embargo-risk_loadEmbargoRiskDataCron_CL_prd,2025-01-31 16:38:26 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,,128,120
automation-ops_segment-update_api_mx_prd,2025-01-14 22:37:31 UTC,2025-07-29 05:50:00 UTC,nodejs20.x,Example description,128,90
mytrends-cl-prd-remove-trends-without-alert,2024-10-29 20:41:16 UTC,2025-07-29 11:50:00 UTC,nodejs20.x,,128,30
risk_automatic-attribute_processOrder_mx_prd,2025-07-14 13:38:52 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,Example description,1024,100
automation-ops_requirements-events_resetByOrderType_CL_prd,2025-07-10 20:50:29 UTC,2025-07-28 16:50:00 UTC,nodejs20.x,,512,30
growth_monetization_onboardingReminders_cl_prd,2025-07-08 21:30:34 UTC,2025-07-29 15:50:00 UTC,nodejs20.x,Example description,512,180
growth_monetization_phoneEvents_mx_prd,2025-07-14 19:01:50 UTC,No disponible,nodejs20.x,Example description,1536,600
risk_sigfe_status_CL_prd,2024-10-08 14:52:36 UTC,No disponible,nodejs20.x,,128,90
risk_sls-finantial-documents-monitor_eventSummary_MX_prd,2025-07-28 13:55:31 UTC,No disponible,nodejs20.x,,1024,120
automation-ops_document-signature_documentSigning_MX_prd,2025-01-14 22:38:24 UTC,2025-07-29 18:50:00 UTC,nodejs20.x,,256,30
growth_monetization_sqsMappingManager_cl_prd,2025-06-18 18:36:02 UTC,2025-07-29 12:50:00 UTC,nodejs20.x,Example description,512,29
client-application-cl-prd-authentication-sync-user,2025-07-28 20:38:57 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,,256,180
automation-ops_user-log_userLogSaveByQueue_CL_prd,2025-01-06 22:06:56 UTC,No disponible,nodejs20.x,,512,30
risk_crif_portfolio_CL_prd,2025-04-24 14:44:53 UTC,No disponible,nodejs20.x,,128,900
growth_monetization_eventHandler-OrderToDeposit_cl_prd,2025-07-14 19:01:51 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,Example description,512,180
automation-ops_state-order_automationStateOrder_mx_prd,2025-07-29 00:29:29 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,,512,30
automation-ops_requirements_updateInvoiceRequirements_CL_prd,2025-07-24 14:59:20 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,,512,90
mytrends-mx-prd-set-new-suppliers,2024-10-29 20:42:31 UTC,No disponible,nodejs20.x,,128,30
risk_automatic-attribute_processRequery_mx_prd,2025-06-26 19:10:37 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,Example description,1024,240
client-application-cl-prd-notifications,2025-07-28 20:37:30 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,,256,60
automation-ops_requirements_deleteOrderRequirements_MX_prd,2025-02-17 20:16:07 UTC,No disponible,nodejs20.x,,512,90
conciliation_sls-accounts_getAccountActivities_cl_prd,2025-01-24 02:04:18 UTC,2025-07-29 16:50:00 UTC,nodejs20.x,Example description,128,30
automation-ops_requirements_updateOrderRequirements_CL_prd,2025-07-24 14:59:06 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,,512,90
risk_automatic-attribute_processLDC_cl_prd,2025-07-29 17:59:19 UTC,2025-07-28 16:50:00 UTC,nodejs20.x,Example description,512,240
automation-ops_state-order_notifyAutomationDelay_cl_prd,2025-07-29 00:25:11 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,,512,30
automation-ops_requirements-events_noDebt_CL_prd,2025-01-02 17:02:21 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,,256,30
collections_mailing_runAvailableTriggers_cl_prd,2025-05-19 20:28:03 UTC,No disponible,nodejs20.x,Send available triggers to prepare emails,128,30
growth_monetization_realTimeOpportunities_cl_prd,2025-07-29 16:32:36 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,Example description,2048,180
automation-ops_state-order_automationNotifyRequirements_cl_prd,2025-07-29 00:25:05 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,,512,30
client-application-mx-prd-authentication-sync-user,2025-07-28 20:41:22 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,,256,180
risk_automatic-attribute_bulkEnqueue_mx_prd,2025-06-26 19:39:58 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,Example description,1024,60
debt_payer_contribution_sync_CL_prd_trigger_fn,2024-12-04 14:11:20 UTC,No disponible,nodejs20.x,,128,900
risk_compliance_opinion_enqueue_mx_prd,2025-07-22 17:50:12 UTC,2025-07-29 18:50:00 UTC,nodejs20.x,,128,60
collections_mailing_prepareEmails_mx_prd,2025-02-19 12:24:11 UTC,2025-07-29 15:50:00 UTC,nodejs20.x,Get debts and prepare the information to send the emails,1024,900
growth_monetization_autokamCustomerTags_mx_prd,2025-07-14 15:13:57 UTC,2025-07-29 11:50:00 UTC,nodejs20.x,Example description,512,300
collections_contacts-api_updateBusinessContacts_mx_prd,2024-06-12 16:29:39 UTC,No disponible,nodejs20.x,Example description,128,30
risk_automatic-attribute_processRequery_cl_prd,2025-07-29 17:59:19 UTC,2025-07-29 19:50:00 UTC,nodejs20.x,Example description,1024,240
risk_pipeline-mx_processAssignCreditLine_MX_prd,2025-04-07 18:58:29 UTC,2025-07-29 18:50:00 UTC,nodejs20.x,,128,180
client-application-cl-prd-apm-serverless-create-payment,2025-07-28 21:14:46 UTC,2025-07-29 18:55:00 UTC,nodejs20.x,,256,300
risk_sigfe_getPossibleSigfeInvoices_CL_prd,2024-10-08 14:52:38 UTC,No disponible,nodejs20.x,,128,60
client-application-mx-prd-documents-generate-pdf-react,2025-07-28 20:40:02 UTC,No disponible,nodejs18.x,,2048,300
automation-ops_download-xml_downloadInvoice_mx_prd,2025-01-14 22:38:11 UTC,No disponible,nodejs20.x,Example description,256,30
automation-ops_requirements_getExceptionsByRequirement_CL_prd,2025-02-17 20:16:19 UTC,2025-07-29 18:55:00 UTC,nodejs20.x,,512,90
risk_sigfe_saveSigfeInvoice_CL_prd,2024-10-08 14:52:37 UTC,No disponible,nodejs20.x,,1024,60
growth_monetization_whatsappSender_cl_prd,2025-07-08 21:30:17 UTC,2025-07-29 15:55:00 UTC,nodejs20.x,Example description,512,90
client-application-cl-prd-authentication-migrate-users,2025-07-28 20:38:47 UTC,No disponible,nodejs20.x,,256,900
debt_rate_event_forwarding_CL_prd_fn,2024-12-04 14:11:29 UTC,2025-07-29 18:55:00 UTC,nodejs20.x,,128,30
collections_mailing_debtByOrderInvoiceIds_cl_prd,2025-05-19 20:28:00 UTC,No disponible,nodejs20.x,endpoint to get debt by order invoice ids from debts repository,128,30
collections_mailing_debtByCreditLine_mx_prd,2025-02-19 12:24:00 UTC,No disponible,nodejs20.x,endpoint to get debt by credit line from debts repository,128,30
client-application-mx-prd-authentication-create-auth-challenge,2025-07-28 20:41:20 UTC,No disponible,nodejs20.x,,256,30
automation-ops_requirements-events_documents_CL_prd,2025-01-02 17:02:23 UTC,2025-07-29 18:55:00 UTC,nodejs20.x,,512,30
automation-ops_communications-hub_subsMessage_CL_prd,2024-12-02 23:22:38 UTC,2025-07-29 18:55:00 UTC,nodejs20.x,,256,30
growth_monetization_financialData_cl_prd,2025-07-08 21:30:09 UTC,2025-07-29 15:55:00 UTC,nodejs20.x,Example description,512,90
automation-ops_urania-api_segments_cl_prd,2024-10-23 22:55:44 UTC,2025-07-29 05:55:00 UTC,nodejs20.x,Example description,512,60
risk_sls-creditline-recurrence_getBusiness_MX_prd,2025-04-24 18:09:03 UTC,2025-07-29 08:55:00 UTC,nodejs20.x,,2048,840
risk_xepelin-network_getBusiness_MX_prd,2025-03-21 15:55:35 UTC,2025-07-29 08:55:00 UTC,nodejs20.x,,2048,900
client-application-cl-prd-documents-generate-pdf-react,2025-07-28 20:37:21 UTC,No disponible,nodejs18.x,,2048,300
risk_xtree_processMassiveLDC_mx_prd,2025-06-10 13:21:18 UTC,2025-07-29 18:55:00 UTC,nodejs20.x,Example description,1048,30
conciliation_sls-accounts_manageActivities_cl_prd,2025-01-24 02:04:20 UTC,2025-07-29 14:55:00 UTC,nodejs20.x,Example description,128,60
risk_xtree_processLDC_cl_prd,2025-07-29 15:39:31 UTC,2025-07-28 15:55:00 UTC,nodejs20.x,Example description,2048,300
risk_automatic-attribute_enqueueFileOnDemandMXV2_mx_prd,2025-06-26 19:40:10 UTC,2025-07-28 15:55:00 UTC,nodejs20.x,Example description,128,900
automation-ops_requirements-events_orderCreatedEvent_CL_prd,2025-07-10 20:50:22 UTC,2025-07-29 18:55:00 UTC,nodejs20.x,,512,30
collections_mailing_debtSummaryProducts_cl_prd,2025-05-19 20:28:20 UTC,No disponible,nodejs20.x,endpoint to get debt summary products from debts repository,128,30
automation-ops_requirements_getOrderRequirements_MX_prd,2025-02-17 20:16:13 UTC,2025-07-29 18:55:00 UTC,nodejs20.x,,512,90
risk_boletin-concursal_enqueueBankruptIdentifiers_CL_prd,2024-10-29 06:40:28 UTC,2025-07-29 05:55:00 UTC,nodejs20.x,,1024,900
growth_monetization_eventHandler-OrderCreated_mx_prd,2025-07-14 19:01:36 UTC,2025-07-29 18:55:00 UTC,nodejs20.x,Example description,512,180
risk_xtree_processMassiveLDC_cl_prd,2025-06-10 13:21:21 UTC,No disponible,nodejs20.x,Example description,1048,30
risk_xtree_processInvoiceCotizacion_cl_prd,2025-07-23 17:36:47 UTC,No disponible,nodejs20.x,Example description,1024,90
risk_siger_service_postSigerWebhookMX_mx_prd,2024-09-09 18:45:52 UTC,2025-07-29 18:55:00 UTC,nodejs20.x,,512,60
risk_embargo-risk_status_CL_prd,2025-01-20 14:06:58 UTC,No disponible,nodejs20.x,,128,30
growth_monetization_autokamEventsManager_cl_prd,2025-07-29 17:22:08 UTC,2025-07-29 18:55:00 UTC,nodejs20.x,Example description,1024,180
risk_yes-but_subscribe_MX_prd,2024-10-24 15:27:36 UTC,No disponible,nodejs20.x,,128,30
growth_monetization_playbooksSignalsTriggerer_cl_prd,2025-07-08 21:30:22 UTC,2025-07-29 18:55:00 UTC,nodejs20.x,Example description,256,120
risk_sls-creditline-increase-request_updateRequest_MX_prd,2025-03-14 15:21:45 UTC,2025-07-20 05:55:00 UTC,nodejs20.x,,1024,120
growth_monetization_phoneEvents_cl_prd,2025-07-14 19:02:12 UTC,2025-07-29 18:55:00 UTC,nodejs20.x,Example description,512,180
core-sofipo_operational-reports_accounting-transform_mx_prd,2025-05-23 18:55:12 UTC,2025-07-29 09:55:00 UTC,nodejs20.x,Lambda function responsible for transforming data to generate accounting.,256,240
risk_sls-creditline-recurrence_saveBusiness_MX_prd,2025-02-10 18:39:22 UTC,2025-07-29 08:55:00 UTC,nodejs20.x,,1024,120
risk_automatic-attribute_enqueueValuesCorpsCL_cl_prd,2025-07-29 17:58:40 UTC,2025-07-15 05:55:00 UTC,nodejs20.x,Example description,128,900
risk_automatic-attribute_enqueueFileOnDemandLDC_mx_prd,2025-06-26 18:59:56 UTC,2025-07-04 18:55:00 UTC,nodejs20.x,Example description,128,900
risk_yes-but_archiveFiles_MX_prd,2024-11-07 14:48:11 UTC,2025-07-25 23:55:00 UTC,nodejs20.x,,128,30
risk_variables-api_variableOrchestrator_CL_prd,2025-05-20 20:10:44 UTC,No disponible,nodejs20.x,,2048,840
risk_siger_service_enqueueStakeholderCreated_mx_prd,2024-09-09 18:45:52 UTC,2025-07-29 18:55:00 UTC,nodejs20.x,,128,60
automation-ops_order-confirmation-corps_appendInvoice_MX_prd,2025-05-20 18:54:00 UTC,2025-07-29 18:55:00 UTC,nodejs20.x,,512,30
risk_sls-creditline-migration_kafkaOrderApprovedFund_MX_prd,2025-07-24 18:23:39 UTC,2025-07-29 18:55:00 UTC,nodejs20.x,,512,60
growth_acq-center_hubspotHandler_MX_prd,2025-06-25 19:17:56 UTC,No disponible,nodejs20.x,Example description,128,180
risk_automatic-attribute_kafka2SqsOrderInvoiceAdded_mx_prd,2025-06-26 20:59:08 UTC,2025-07-29 18:55:00 UTC,nodejs20.x,Example description,1024,100
automation-ops_order-confirmation-corps_addInvoices_CL_prd,2025-05-20 18:58:09 UTC,2025-07-28 18:55:00 UTC,nodejs20.x,,512,30
automation-ops_requirements-events_manageDocuments_CL_prd,2025-03-05 21:14:51 UTC,No disponible,nodejs20.x,,512,30
automation-ops_order-confirmation_updateAnalyst_CL_prd,2025-03-20 16:40:47 UTC,2025-07-29 18:55:00 UTC,nodejs20.x,,256,30
risk_sls-creditline-migration_processKafkaEvents_MX_prd,2025-07-24 18:23:38 UTC,2025-07-29 18:55:00 UTC,nodejs20.x,,512,60
risk_yes-but_archiveBusinessVariables_MX_prd,2024-10-24 15:27:39 UTC,2025-07-28 23:55:00 UTC,nodejs20.x,,128,30
growth_acq-center_hubspotHandler_CL_prd,2025-06-25 19:17:56 UTC,No disponible,nodejs20.x,Example description,128,180
risk_xtree_processLDC_mx_prd,2025-07-29 15:39:44 UTC,2025-07-29 18:55:00 UTC,nodejs20.x,Example description,2048,300
risk_automatic-attribute_sendSlackErrorsMessage_mx_prd,2025-06-26 18:59:46 UTC,2025-07-29 16:55:00 UTC,nodejs20.x,Example description,128,3
mytrends-cl-prd-process-page,2024-10-29 20:40:58 UTC,2025-07-29 11:55:00 UTC,nodejs20.x,,128,60
automation-ops_requirements_getExceptionsByRequirement_MX_prd,2025-02-17 20:16:05 UTC,2025-07-29 17:55:00 UTC,nodejs20.x,,512,90
risk_automatic-attribute_enqueueReplComplianceCL_cl_prd,2025-07-29 17:59:01 UTC,2025-07-29 06:55:00 UTC,nodejs20.x,Example description,128,120
growth_acq-center_hubspotWebhook_CL_prd,2025-06-25 19:17:56 UTC,No disponible,nodejs20.x,Example description,128,180
client-application-mx-prd-core-events-pipe-enrichment,2025-07-28 20:40:10 UTC,2025-07-29 18:55:00 UTC,nodejs20.x,,128,30
client-application-cl-prd-authentication-remove-role,2025-07-28 20:38:50 UTC,No disponible,nodejs20.x,,256,30
risk_xtree_notifyErrorsToSlack_cl_prd,2025-05-22 13:10:35 UTC,2025-07-25 20:55:00 UTC,nodejs20.x,Example description,1024,30
growth_monetization_hubspotEventHandler_cl_prd,2025-07-15 20:31:48 UTC,2025-07-29 18:55:00 UTC,nodejs20.x,Example description,512,180
risk_xtree_processLDCEventCL_cl_prd,2025-07-29 15:39:32 UTC,2025-07-29 18:55:00 UTC,nodejs20.x,Example description,1024,120
conciliation_sls-accounts_status_cl_prd,2025-01-24 02:04:18 UTC,No disponible,nodejs20.x,Example description,128,30
risk_automatic-attribute_enqueueFileOnDemandMX_mx_prd,2025-06-26 19:10:36 UTC,No disponible,nodejs20.x,Example description,128,900
automation-ops_requirements-events_bankAccount_CL_prd,2025-01-02 17:02:15 UTC,2025-07-29 18:55:00 UTC,nodejs20.x,,512,30
risk_sls-creditline-migration_kafkaDebtCreated_MX_prd,2025-07-24 18:23:33 UTC,2025-07-29 18:55:00 UTC,nodejs20.x,,512,60
automation-ops_order-confirmation-corps_parseInvoices_MX_prd,2025-04-15 15:58:25 UTC,No disponible,nodejs20.x,,512,30
client-application-mx-prd-apm-serverless-create-payment,2025-07-28 21:15:16 UTC,2025-07-29 18:55:00 UTC,nodejs20.x,,256,300
risk_siger_service_consultRFC_mx_prd,2024-09-09 18:45:51 UTC,No disponible,nodejs20.x,,128,60
collections_mailing_debtByOrderInvoiceIds_mx_prd,2025-02-19 12:24:01 UTC,No disponible,nodejs20.x,endpoint to get debt by order invoice ids from debts repository,128,30
risk_variables-api_vlProcessor_MX_prd,2025-05-09 13:21:18 UTC,No disponible,nodejs20.x,,2048,840
growth_monetization_customerIoWebhook_cl_prd,2025-07-09 16:53:07 UTC,2025-07-29 18:55:00 UTC,nodejs20.x,Example description,512,180
automation-ops_requirements_createExceptions_MX_prd,2025-02-17 20:16:04 UTC,2025-07-29 17:55:00 UTC,nodejs20.x,,512,90
automation-ops_requirements-events_operationCost_CL_prd,2025-01-02 17:02:16 UTC,2025-07-29 18:55:00 UTC,nodejs20.x,,128,30
growth_monetization_customerIoWebhook_mx_prd,2025-07-09 17:17:51 UTC,No disponible,nodejs20.x,Example description,128,180
automation-ops_requirements_getInvoiceRequirements_MX_prd,2025-02-17 20:16:12 UTC,2025-07-29 18:55:00 UTC,nodejs20.x,,512,90
risk_boletin-concursal_dequeueToCheckBankruptcy_CL_prd,2024-10-29 14:10:59 UTC,2025-07-29 18:55:00 UTC,nodejs20.x,,128,60
risk_sls-creditline-recurrence_creditLineRequest_MX_prd,2025-04-24 18:09:06 UTC,No disponible,nodejs20.x,,1024,120
automation-ops_requirements_softDeleteException_CL_prd,2025-07-24 14:59:13 UTC,2025-07-28 19:55:00 UTC,nodejs20.x,,512,90
risk_sls-finantial-documents-monitor_eventSummary_CL_prd,2025-07-28 13:56:10 UTC,No disponible,nodejs20.x,,1024,120
risk_automatic-attribute_enqueueFileOnDemandV2_cl_prd,2025-07-29 17:59:29 UTC,2025-07-28 13:55:00 UTC,nodejs20.x,Example description,512,900
risk_yes-but_saveBusinessVariableFile_MX_prd,2024-11-07 14:48:11 UTC,2025-07-28 18:55:00 UTC,nodejs20.x,,128,30
risk_sls-buro-credito_saveAuditBuro_MX_prd,2025-03-12 13:50:34 UTC,2025-07-29 18:55:00 UTC,nodejs20.x,,2048,30
growth_monetization_callClient_cl_prd,2025-06-18 18:35:54 UTC,2025-07-29 17:55:00 UTC,nodejs20.x,Example description,512,180
growth_monetization_eventHandler-OrderCreated_cl_prd,2025-07-14 19:02:13 UTC,2025-07-29 18:55:00 UTC,nodejs20.x,Example description,512,180
automation-ops_order-confirmation-corps_getInvoices_CL_prd,2025-04-28 15:43:12 UTC,No disponible,nodejs20.x,,512,30
conciliation_sls-accounts_checkBalance_cl_prd,2025-01-24 02:04:19 UTC,2025-07-29 06:55:00 UTC,nodejs20.x,Example description,128,30
growth_monetization_autokamEventsManager_mx_prd,2025-07-29 17:14:16 UTC,2025-07-29 18:55:00 UTC,nodejs20.x,Example description,512,59
automation-ops_requirements_createInvoiceRequirements_MX_prd,2025-02-17 20:16:06 UTC,2025-07-29 15:55:00 UTC,nodejs20.x,,512,90
collections_contacts-api_getBusinessContacts_mx_prd,2024-06-12 16:29:38 UTC,2025-07-29 18:55:00 UTC,nodejs20.x,Example description,128,30
growth_monetization_eventHandler-OrderReviewed_cl_prd,2025-07-14 19:02:09 UTC,2025-07-29 19:55:00 UTC,nodejs20.x,Example description,512,180
collections_mailing_debtByIdentifier_cl_prd,2025-05-19 20:28:02 UTC,No disponible,nodejs20.x,endpoint to get debt by identifier from debts repository,128,30
risk_automatic-attribute_bulkServiceOrder_cl_prd,2025-07-29 17:58:43 UTC,2025-07-29 19:55:00 UTC,nodejs20.x,Example description,1024,100
automation-ops_requirements_getOrderRequirements_CL_prd,2025-02-17 20:16:17 UTC,2025-07-29 19:55:00 UTC,nodejs20.x,,512,90
automation-ops_state-order_notifyAutomationDelay_mx_prd,2025-07-29 00:29:41 UTC,2025-07-29 18:55:00 UTC,nodejs20.x,,512,30
risk_siger_service_processPeriodicalCreditLine_mx_prd,2024-09-09 18:45:53 UTC,2025-07-17 05:55:00 UTC,nodejs20.x,,128,60
client-application-cl-prd-authentication-verify-auth-challenge,2025-07-28 20:38:52 UTC,No disponible,nodejs20.x,,256,30
risk_sls-creditline-increase-request_expireRequest_MX_prd,2025-03-14 15:21:45 UTC,2025-07-29 05:55:00 UTC,nodejs20.x,,1024,120
risk_sls-creditline-migration_calculateBalance_MX_prd,2025-07-18 13:57:46 UTC,2025-07-29 19:55:00 UTC,nodejs20.x,,128,60
automation-ops_requirements-events_documents_MX_prd,2025-01-14 22:38:30 UTC,2025-07-29 19:55:00 UTC,nodejs20.x,,512,30
risk_xtree_kafka2SqsOrderReviewed_mx_prd,2025-05-29 15:53:50 UTC,2025-07-29 18:55:00 UTC,nodejs20.x,Example description,1024,60
risk_yes-but_enqueueExpiredFiles_MX_prd,2024-10-24 15:27:36 UTC,2025-07-28 23:55:00 UTC,nodejs20.x,,128,30
risk_automatic-attribute_createTgrDebt_cl_prd,2025-07-29 17:58:42 UTC,2025-07-19 18:55:00 UTC,nodejs20.x,Example description,1024,240
automation-ops_check-order_checkOrderTransfers_MX_prd,2025-07-10 21:20:19 UTC,2025-07-29 19:55:00 UTC,nodejs20.x,,512,30
risk_sls-creditline-migration_kafkaOrderApprovedFund_CL_prd,2025-07-24 18:23:32 UTC,2025-07-29 18:55:00 UTC,nodejs20.x,,512,60
collections_mailing_getPayers_mx_prd,2025-02-19 12:24:02 UTC,No disponible,nodejs20.x,endpoint to get payers from debts repository,128,30
automation-ops_requirements_updateOrderRequirements_MX_prd,2025-02-17 20:16:17 UTC,2025-07-29 19:55:00 UTC,nodejs20.x,,512,90
automation-ops_kams-management_getKamsByBusinessId_MX_prd,2024-12-12 22:53:58 UTC,2025-07-29 19:55:00 UTC,nodejs20.x,,256,30
collections_contacts-api_deleteBusinessContact_mx_prd,2024-06-12 16:29:38 UTC,No disponible,nodejs20.x,Example description,128,30
risk_sigfe_enqueueSigfeMark_CL_prd,2024-10-08 14:52:37 UTC,No disponible,nodejs20.x,,128,90
automation-ops_document-generation_updateGeneration_MX_prd,2024-12-13 17:24:51 UTC,2025-07-03 16:55:00 UTC,nodejs20.x,,512,30
debt_debt_hard_collection_CL_prd_fn,2024-12-04 14:11:27 UTC,No disponible,nodejs20.x,,128,30
risk_variables-api_variableWebhook_MX_prd,2025-05-16 21:31:15 UTC,No disponible,nodejs20.x,,2048,840
automation-ops_requirements_createExceptions_CL_prd,2025-07-24 14:58:45 UTC,2025-07-29 18:55:00 UTC,nodejs20.x,,512,90
automation-ops_requirements_softDeleteException_MX_prd,2025-02-17 20:16:12 UTC,2025-07-24 21:55:00 UTC,nodejs20.x,,512,90
risk_yes-but_getBusinessVariableFiles_MX_prd,2024-11-07 14:48:15 UTC,2025-07-29 19:55:00 UTC,nodejs20.x,,128,30
debt_activate_debt_CL_prd_fn,2024-12-04 14:11:20 UTC,2025-07-29 19:55:00 UTC,nodejs20.x,,128,60
risk_siger_service_sendSlackErrors_mx_prd,2024-09-09 18:45:50 UTC,2025-07-17 05:55:00 UTC,nodejs20.x,,128,60
growth_monetization_playbooksSignalsTriggerer_mx_prd,2025-07-08 21:29:58 UTC,2025-07-28 15:55:00 UTC,nodejs20.x,Example description,256,120
automation-ops_download-xml_notifyInvoice_mx_prd,2025-01-14 22:38:10 UTC,No disponible,nodejs20.x,Example description,128,30
client-application-cl-prd-documents-generate-pdf,2025-07-28 20:37:25 UTC,2025-07-29 19:55:00 UTC,nodejs18.x,,256,300
mytrends-mx-prd-set-new-suppliers-blocklisted,2024-10-29 20:42:19 UTC,No disponible,nodejs20.x,,128,30
risk_sigfe_enqueue_CL_prd,2024-10-08 14:52:36 UTC,No disponible,nodejs20.x,,128,30
automation-ops_requirements-events_noHoldInvoices_MX_prd,2025-01-14 22:38:31 UTC,2025-07-29 18:55:00 UTC,nodejs20.x,,256,30
risk_sls-creditline-migration_partialPays_CL_prd,2025-07-18 13:57:56 UTC,2025-07-29 17:55:00 UTC,nodejs20.x,,128,30
risk_xtree_processInvoices_cl_prd,2025-07-29 15:39:32 UTC,2025-07-29 19:55:00 UTC,nodejs20.x,Example description,2048,720
growth_monetization_eventHandler-OrderRejected_cl_prd,2025-07-14 19:02:16 UTC,2025-07-29 18:55:00 UTC,nodejs20.x,Example description,512,180
risk_siger_service_processStakeholderConsult_mx_prd,2024-09-09 18:45:53 UTC,2025-07-29 18:55:00 UTC,nodejs20.x,,128,60
conciliation_sls-accounts_getAllAccounts_cl_prd,2025-01-24 02:04:20 UTC,2025-07-29 16:55:00 UTC,nodejs20.x,Example description,128,30
xepelin-pdf-generator-mx-prd-generate-pdf,2024-06-21 22:13:59 UTC,No disponible,nodejs18.x,,128,20
collections_mailing_debtByCreditLine_cl_prd,2025-05-19 20:27:59 UTC,No disponible,nodejs20.x,endpoint to get debt by credit line from debts repository,128,30
risk_variables-api_vlProcessor_CL_prd,2025-05-09 13:24:55 UTC,No disponible,nodejs20.x,,2048,840
debt_debt_q_MX_prd_fn,2025-07-22 16:49:01 UTC,2025-07-17 07:55:00 UTC,nodejs20.x,,128,30
core-sofipo_notification-center_slack-notification_mx_prd,2025-06-19 14:30:27 UTC,No disponible,nodejs20.x,Lambda function responsible for exposing rest service to send notifications to slack.,256,240
growth_monetization_newActivationEventFilter_mx_prd,2025-06-18 18:35:16 UTC,No disponible,nodejs20.x,Example description,128,29
risk_variables-api_apiEndpoint_CL_prd,2025-05-20 20:10:50 UTC,No disponible,nodejs20.x,,2048,840
risk_sls-creditline-migration_calculateBalanceBulk_MX_prd,2025-07-18 19:45:30 UTC,2025-07-18 22:55:00 UTC,nodejs20.x,,128,60
risk_sls-creditline-migration_kafkaOrderRejected_CL_prd,2025-07-24 18:23:31 UTC,2025-07-29 18:55:00 UTC,nodejs20.x,,512,60
risk_yes-but_processGandalfRow_MX_prd,2024-10-24 15:27:38 UTC,No disponible,nodejs20.x,,128,30
risk_sls-finantial-documents-monitor_eventProcessor_CL_prd,2025-07-28 13:56:10 UTC,2025-07-29 19:55:00 UTC,nodejs20.x,,1024,120
debt_send_daily_process_messages_MX_prd_trigger_fn,2025-07-22 17:06:28 UTC,2025-07-29 05:55:00 UTC,nodejs20.x,,128,900
risk_sls-creditline-migration_kafkaOrderRejected_MX_prd,2025-07-24 18:23:32 UTC,2025-07-29 18:55:00 UTC,nodejs20.x,,512,60
client-application-cl-prd-core-events-pipe-enrichment,2025-07-28 20:37:15 UTC,2025-07-29 19:55:00 UTC,nodejs20.x,,128,30
core-sofipo_core-banking_transaction-logger_mx_prd,2025-06-12 16:59:43 UTC,No disponible,nodejs20.x,Example description,128,60
client-application-mx-prd-notifications,2025-07-28 20:40:08 UTC,2025-07-29 19:55:00 UTC,nodejs20.x,,256,60
risk_xepelin-network_processInvoices_CL_prd,2025-07-29 13:14:25 UTC,2025-07-29 18:55:00 UTC,nodejs20.x,,1024,60
automation-ops_order-confirmation-corps_addInvoices_MX_prd,2025-05-20 18:54:06 UTC,No disponible,nodejs20.x,,512,30
growth_monetization_pdfGenerator_mx_prd,2024-09-13 17:29:17 UTC,No disponible,nodejs20.x,Example description,128,60
automation-ops_requirements_getInvoiceReqsByOrders_MX_prd,2025-02-17 20:16:05 UTC,No disponible,nodejs20.x,,512,90
risk_sls-creditline-migration_calculateBalanceBulk_CL_prd,2025-07-18 19:46:30 UTC,2025-07-18 18:55:00 UTC,nodejs20.x,,128,60
debt_send_daily_process_messages_CL_prd_trigger_fn,2024-12-04 14:11:27 UTC,2025-07-29 05:55:00 UTC,nodejs20.x,,128,900
automation-ops_requirements_filterOrderRequirements_CL_prd,2025-02-17 20:16:23 UTC,2025-07-29 12:55:00 UTC,nodejs20.x,,512,90
automation-ops_requirements-events_operationCost_MX_prd,2025-01-14 22:38:32 UTC,2025-07-29 18:55:00 UTC,nodejs20.x,,128,30
collections_mailing_runAvailableTriggers_mx_prd,2025-02-19 12:24:02 UTC,2025-07-29 15:55:00 UTC,nodejs20.x,Send available triggers to prepare emails,128,30
mytrends-cl-prd-save-trends-in-alert,2024-10-29 20:41:46 UTC,2025-07-29 11:55:00 UTC,nodejs20.x,,128,30
client-application-cl-prd-authentication-assign-role,2025-07-28 20:39:00 UTC,2025-07-29 18:55:00 UTC,nodejs20.x,,256,30
risk_xtree_updateLDCEvent_mx_prd,2025-05-22 13:10:28 UTC,2025-07-29 18:55:00 UTC,nodejs20.x,Example description,1024,60
collections_mailing_getPayers_cl_prd,2025-05-19 20:28:01 UTC,No disponible,nodejs20.x,endpoint to get payers from debts repository,128,30
debt_server_global_proxy_MX_prd_fn,2025-07-22 16:48:46 UTC,2025-07-09 19:55:00 UTC,nodejs20.x,,128,30
growth_monetization_callClient_mx_prd,2025-06-26 13:40:22 UTC,2025-07-29 18:55:00 UTC,nodejs20.x,Example description,512,180
risk_yes-but_createBusinessVariables_MX_prd,2024-11-07 14:48:14 UTC,2025-07-28 17:55:00 UTC,nodejs20.x,,128,30
mytrends-mx-prd-get-all-trends,2024-10-29 20:42:49 UTC,2025-07-29 14:55:00 UTC,nodejs20.x,,128,60
automation-ops_requirements-events_resetByOrderType_MX_prd,2025-07-10 20:45:00 UTC,No disponible,nodejs20.x,,512,30
collections_mailing_debtByIdentifier_mx_prd,2025-02-19 12:24:00 UTC,No disponible,nodejs20.x,endpoint to get debt by identifier from debts repository,128,30
risk_sigfe_calculateSigfeMark_CL_prd,2024-10-08 14:52:37 UTC,No disponible,nodejs20.x,,128,90
risk_yes-but_status_MX_prd,2024-10-24 15:27:35 UTC,No disponible,nodejs20.x,,128,30
automation-ops_requirements_getInvoiceReqsByOrders_CL_prd,2025-02-17 20:16:49 UTC,2025-07-28 14:55:00 UTC,nodejs20.x,,512,90
automation-ops_requirements-events_verifyByDemand_CL_prd,2025-01-02 17:02:23 UTC,2025-07-29 19:55:00 UTC,nodejs20.x,,1024,30
risk_siger_service_expireRequestMX_mx_prd,2024-09-09 18:45:53 UTC,2025-07-29 05:55:00 UTC,nodejs20.x,,128,60
risk_automatic-attribute_enqueueFileOnDemandLDC_cl_prd,2025-07-29 17:58:59 UTC,2025-07-28 16:55:00 UTC,nodejs20.x,Example description,512,900
growth_monetization_eventHandler-OrderAppealed_mx_prd,2025-07-14 19:01:43 UTC,2025-07-29 18:55:00 UTC,nodejs20.x,Example description,512,180
client-application-cl-prd-authentication-check-sync-user,2025-07-28 20:39:01 UTC,No disponible,nodejs20.x,,256,900
automation-ops_requirements_updateException_CL_prd,2025-02-17 20:16:24 UTC,2025-07-28 16:55:00 UTC,nodejs20.x,,512,90
risk_sls-creditline-increase-request_readRequest_MX_prd,2025-07-25 18:26:55 UTC,2025-07-25 17:55:00 UTC,python3.9,,2048,480
risk_pipeline-mx_status_MX_prd,2024-11-21 20:57:30 UTC,No disponible,nodejs20.x,,128,30
risk_automatic-attribute_enqueueDicomStakeholder_cl_prd,2025-07-29 17:59:02 UTC,No disponible,nodejs20.x,Example description,128,120
conciliation_sls-accounts_createActivity_cl_prd,2025-01-24 02:04:19 UTC,2025-07-29 16:55:00 UTC,nodejs20.x,Example description,128,30
risk_compliance_opinion_dlqSlackNotifier_mx_prd,2025-07-22 17:50:11 UTC,No disponible,nodejs20.x,,512,60
risk_sls-creditline-increase-request_saveRequest_MX_prd,2025-02-05 14:00:06 UTC,2025-07-25 17:55:00 UTC,nodejs20.x,,1024,120
mytrends-cl-prd-send-email-on-trend-in-alert,2024-10-29 20:41:40 UTC,No disponible,nodejs20.x,,128,60
risk_automatic-attribute_generateReportFileOnDemand_cl_prd,2025-07-29 17:58:59 UTC,2025-07-28 16:55:00 UTC,nodejs20.x,Example description,1024,600
client-application-cl-prd-authentication-migrate-user,2025-07-28 20:38:55 UTC,2025-07-29 18:55:00 UTC,nodejs20.x,,256,300
collections_mailing_debtSummaryProducts_mx_prd,2025-02-19 12:24:01 UTC,No disponible,nodejs20.x,endpoint to get debt summary products from debts repository,128,30
risk_xtree_notifyLDCErrorsToSlack_mx_prd,2025-05-22 13:10:26 UTC,No disponible,nodejs20.x,Example description,1024,30
growth_monetization_eventHandler-OrderActive_cl_prd,2025-07-14 19:02:09 UTC,2025-07-29 19:55:00 UTC,nodejs20.x,Example description,512,180
risk_xepelin-network_processBusiness_MX_prd,2025-07-29 13:14:17 UTC,2025-07-29 08:55:00 UTC,nodejs20.x,,1024,60
automation-ops_requirements-events_verifyByDemand_MX_prd,2025-01-14 22:38:32 UTC,2025-07-29 19:55:00 UTC,nodejs20.x,,1024,30
debt_payer_contribution_sync_MX_prd_trigger_fn,2025-07-22 16:48:48 UTC,No disponible,nodejs20.x,,128,900
risk_xtree_kafka2SqsOrderReviewed_cl_prd,2025-05-29 15:53:56 UTC,2025-07-29 19:55:00 UTC,nodejs20.x,Example description,1024,60
risk_sls-buro-credito_getAuditBuro_MX_prd,2025-03-12 13:50:39 UTC,2025-07-29 18:55:00 UTC,nodejs20.x,,2048,30
