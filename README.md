# Script para Obtener Información de Funciones Lambda

Este script obtiene información de todas las funciones Lambda de una cuenta de AWS, incluyendo:
- Nombre de la función
- Fecha de creación
- Fecha de última actividad (invocación)
- Runtime utilizado
- Descripción
- Memoria asignada
- Timeout configurado

## Requisitos

- Python 3.6 o superior
- Credenciales de AWS configuradas
- Permisos de AWS necesarios:
  - `lambda:ListFunctions`
  - `cloudwatch:GetMetricStatistics`

## Instalación

1. Instalar las dependencias:
```bash
pip install -r requirements.txt
```

2. Configurar credenciales de AWS (una de las siguientes opciones):

### Opción 1: AWS CLI
```bash
aws configure
```

### Opción 2: Variables de entorno
```bash
export AWS_ACCESS_KEY_ID=tu_access_key
export AWS_SECRET_ACCESS_KEY=tu_secret_key
export AWS_DEFAULT_REGION=us-east-1
```

### Opción 3: Perfil de IAM (si ejecutas en EC2)
Asignar un rol de IAM con los permisos necesarios a la instancia EC2.

## Uso

### Uso básico
```bash
python get_lambda_info.py
```

### Especificar región
```bash
python get_lambda_info.py --region us-west-2
```

### Usar perfil específico de AWS
```bash
python get_lambda_info.py --profile xescapital
```

### Usar perfil con SSO
```bash
# Si el perfil usa SSO, primero autenticarse
aws sso login --profile xescapital

# Luego ejecutar el script
python get_lambda_info.py --profile xescapital
```

### Salida en formato JSON
```bash
python get_lambda_info.py --format json --profile xescapital
```

### Salida en formato CSV
```bash
python get_lambda_info.py --format csv --profile xescapital
```

### Guardar salida en archivo (automático)
```bash
# CSV - se guarda automáticamente como lambda_info_xescapital.csv
python get_lambda_info.py --format csv --profile xescapital

# JSON - se guarda automáticamente como lambda_info_xescapital.json
python get_lambda_info.py --format json --profile xescapital

# Para múltiples perfiles
python get_lambda_info.py --format csv --profile platform  # -> lambda_info_platform.csv
python get_lambda_info.py --format csv --profile production  # -> lambda_info_production.csv
```

### Especificar archivo de salida personalizado
```bash
python get_lambda_info.py --format csv --profile xescapital --output mi_reporte.csv
```

## Ejemplos de Salida

### Formato Tabla (default)
```
========================================================================================================================
Nombre de la Función                     Fecha Creación            Última Invocación         Runtime        
========================================================================================================================
mi-funcion-lambda                        2024-01-15 10:30:45 UTC   2024-01-20 14:22:10 UTC   python3.9      
otra-funcion                             2024-01-10 09:15:30 UTC   No disponible             nodejs18.x     
========================================================================================================================
Total de funciones Lambda: 2
```

### Formato JSON
```json
[
  {
    "name": "mi-funcion-lambda",
    "created_date": "2024-01-15 10:30:45 UTC",
    "last_invocation": "2024-01-20 14:22:10 UTC",
    "runtime": "python3.9",
    "description": "Función para procesar datos",
    "memory_size": 128,
    "timeout": 30
  }
]
```

## Permisos de IAM Necesarios

Crear una política de IAM con los siguientes permisos:

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "lambda:ListFunctions"
            ],
            "Resource": "*"
        },
        {
            "Effect": "Allow",
            "Action": [
                "cloudwatch:GetMetricStatistics"
            ],
            "Resource": "*"
        }
    ]
}
```

## Notas Importantes

- El script busca la última actividad en los últimos 30 días
- Si una función no ha sido invocada en los últimos 30 días, aparecerá como "No disponible"
- El proceso puede tomar tiempo si tienes muchas funciones Lambda
- Las fechas se muestran en UTC

## Solución de Problemas

### Error de credenciales
```
Error: No se encontraron credenciales de AWS.
```
**Solución**: Configura tus credenciales de AWS usando `aws configure` o variables de entorno.

### Error de permisos
```
Error al obtener funciones Lambda: An error occurred (AccessDenied)
```
**Solución**: Verifica que tu usuario/rol tenga los permisos necesarios listados arriba.

### Sin funciones Lambda
```
No se encontraron funciones Lambda.
```
**Solución**: Verifica que estés consultando la región correcta donde tienes tus funciones Lambda.
