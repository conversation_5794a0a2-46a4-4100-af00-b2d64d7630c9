name,created_date,last_invocation,runtime,description,memory_size,timeout
platform_quotas_alarm_gl_prod,2024-12-10 12:37:36 UTC,2025-07-29 21:00:00 UTC,python3.10,,128,890
paas-cloudtrail-elb-github-createListenerLambda-6812a5f,2024-10-29 16:30:38 UTC,2025-07-29 17:00:00 UTC,provided.al2,,128,3
CidInitialSetup-DoNotRun,2023-10-17 20:37:16 UTC,No disponible,python3.9,"Do what CFN cannot: start crawler, delete bucket with objects and delete an non empty workgroup",128,300
CidProcessPath-DoNotRun,2023-10-17 20:36:37 UTC,No disponible,python3.9,Do what CFN cannot: process string of path,128,60
documents-change-status-mx-uat,2024-05-13 17:26:44 UTC,No disponible,nodejs20.x,Lambda to update the status of a document,128,3
henis_count,2024-11-08 13:58:37 UTC,2025-07-29 21:00:00 UTC,python3.9,It is responsible for counting the number of events related to Lambda functions.,1024,900
documents-draft-cleaner-mx-dev,2024-06-05 15:43:16 UTC,2025-07-29 21:00:00 UTC,nodejs20.x,Lambda function for processing dlq tasks (database clean) on schedule in mx for dev,128,60
CidCustomResourceDashboard,2023-10-17 20:37:05 UTC,No disponible,python3.9,"A lambda that manage create delete update of Athena views, QuickSight Datasets and dashboards using CID-CMD tool",2688,300
aws-cost-report-lambda-prod,2025-05-13 18:08:49 UTC,2025-07-28 07:00:00 UTC,python3.9,,512,300
documents-draft-cleaner-mx-rc,2024-05-15 14:04:54 UTC,2025-07-29 21:00:00 UTC,nodejs20.x,Lambda function for processing dlq tasks (database clean) on schedule in mx for rc,128,3
documents-draft-cleaner-mx-uat,2024-05-15 13:45:40 UTC,2025-07-29 21:00:00 UTC,nodejs20.x,Lambda function for processing dlq tasks (database clean) on schedule in mx for uat,128,3
comprobe-cognito-user,2025-03-12 20:56:17 UTC,No disponible,python3.13,,128,3
aws-cost-report-lambda-dev,2025-05-26 18:35:04 UTC,2025-07-28 07:00:00 UTC,python3.9,,512,300
core-engineering_test-lambda_CL_dev,2024-03-22 09:21:42 UTC,No disponible,nodejs18.x,,128,3
aws-controltower-NotificationForwarder,2023-09-21 18:46:29 UTC,2025-07-29 21:00:00 UTC,python3.9,SNS message forwarding function for aggregating account notifications.,128,60
documents-change-status-mx-rc,2024-05-13 17:49:48 UTC,No disponible,nodejs20.x,Lambda to update the status of a document,128,3
platform_check_endpoint_health_gl_prod,2025-04-08 18:46:24 UTC,2025-07-29 21:00:00 UTC,python3.10,,128,900
platform_sns_slack_alert_gl_prod,2024-12-04 20:16:54 UTC,2025-07-29 21:00:00 UTC,python3.10,,128,900
documents-change-status-mx-dev,2024-06-07 12:53:50 UTC,No disponible,nodejs20.x,Lambda to update the status of a document,128,60
